[versions]
agp = "8.6.1"
kotlin = "1.9.25"
dokka = "1.7.20"
protobuf = "0.9.3"

androidx-constraintlayout = "2.1.4"
androidx-navigation-fragment-ktx = "2.5.3"
androidx-navigation-ui-ktx = "2.5.3"
androidx-work-runtime-ktx = "2.7.1"
androidx-preference-ktx = "1.2.0"
androidx-lifecycle-livedata-ktx = "2.5.1"
androidx-lifecycle-viewmodel-ktx = "2.5.1"
androidx-datastore = "1.0.0"
androidx-datastore-preferences = "1.0.0"
androidx-room-runtime  = "2.5.0"
androidx-room-ktx = "2.5.0"
androidx-room-compiler = "2.5.0"
androidx-lifecycle-process = "2.6.1"
google-material = "1.8.0"
androidx-startup="1.1.1"
android-autosize = "v1.2.1"
roundedimageview = "2.3.0"
toast-utils = "10.3"
dialogx = "0.0.47"
xxpermissions = "21.2"
protoc = "2.6.1"
protobuf-java = "2.6.1"
zxingAndroidEmbedded = "4.3.0"
appcompat = "1.7.0"
activity = "1.10.1"
eventbus = "3.3.1"


junit = "4.13.2"
androidx-test-ext = "1.1.5"
espresso-core = "3.5.1"

#amap
amap = "9.8.2_3dmap9.8.2"
amap-search = "9.7.0"

#mapbox
mapbox = "10.16.6"
mapbox-navigation-android = "2.14.0"
mapbox-search-android = "1.0.0-rc.6"
mapbox-search-android-native = "0.71.0"
mapbox-search-android-ui = "1.0.0-rc.6"
mapbox-base-annotations = "0.8.0"
mapbox-base-annotations-processor = "0.8.0"

[libraries]
amap = { module = "com.amap.api:navi-3dmap", version.ref = "amap" }
amap-search = { module = "com.amap.api:search", version.ref = "amap-search" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "androidx-constraintlayout" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "androidx-navigation-fragment-ktx" }
androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "androidx-navigation-ui-ktx" }
androidx-work-runtime-ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "androidx-work-runtime-ktx" }
androidx-preference-ktx = { module = "androidx.preference:preference-ktx", version.ref = "androidx-preference-ktx" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "androidx-lifecycle-livedata-ktx" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "androidx-lifecycle-viewmodel-ktx" }
androidx-datastore = { module = "androidx.datastore:datastore", version.ref = "androidx-datastore" }
androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "androidx-datastore-preferences" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "androidx-room-runtime" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "androidx-room-ktx" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "androidx-room-compiler" }
androidx-lifecycle-process = { module = "androidx.lifecycle:lifecycle-process", version.ref = "androidx-lifecycle-process" }
google-material = { module = "com.google.android.material:material", version.ref = "google-material" }
androidx-startup = {module = "androidx.startup:startup-runtime", version.ref = "androidx-startup"}

android-autosize = { module = "com.github.JessYanCoding:AndroidAutoSize", version.ref = "android-autosize" }
roundedimageview = { module = "com.makeramen:roundedimageview", version.ref = "roundedimageview" }
toast-utils = { module = "com.github.getActivity:ToastUtils", version.ref = "toast-utils" }
dialogx = { module = "com.github.kongzue.DialogX:DialogX", version.ref = "dialogx" }
xxpermissions = { module = "com.github.getActivity:XXPermissions", version.ref = "xxpermissions" }
protoc = { module = "com.google.protobuf:protoc", version.ref = "protoc" }
protobuf-java = { module = "com.google.protobuf:protobuf-java", version.ref = "protobuf-java" }

junit = { module = "junit:junit", version.ref = "junit" }
androidx-test-ext = { module = "androidx.test.ext:junit", version.ref = "androidx-test-ext" }
espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espresso-core" }
mapbox = { module = "com.mapbox.maps:android", version.ref = "mapbox" }
mapbox-navigation-android = { module = "com.mapbox.navigation:android", version.ref = "mapbox-navigation-android" }
mapbox-search-android = { module = "com.mapbox.search:mapbox-search-android", version.ref = "mapbox-search-android" }
mapbox-search-android-native = { module = "com.mapbox.search:mapbox-search-android-native", version.ref = "mapbox-search-android-native" }
mapbox-search-android-ui = { module = "com.mapbox.search:mapbox-search-android-ui", version.ref = "mapbox-search-android-ui" }
mapbox-base-annotations = { module = "com.mapbox.base:annotations", version.ref = "mapbox-base-annotations" }
mapbox-base-annotations-processor = { module = "com.mapbox.base:annotations-processor", version.ref = "mapbox-base-annotations-processor" }
zxing-android-embedded = { module = "com.journeyapps:zxing-android-embedded", version.ref = "zxingAndroidEmbedded" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
eventbus = { module = "org.greenrobot:eventbus", version.ref = "eventbus" }


[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
jetbrains-dokka = { id = "org.jetbrains.dokka", version.ref = "dokka" }
google-protobuf = { id = "com.google.protobuf", version.ref = "protobuf" }
