import com.android.build.api.dsl.AndroidSourceSet
import com.android.build.api.variant.ResValue
import com.android.build.api.variant.impl.VariantOutputImpl
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

val currentBuildTimestamp: String? =
    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm"))

plugins {
    id("com.android.application")
    id("kotlin-android")
    id("com.google.protobuf")
    id("kotlin-parcelize")
    id("com.google.devtools.ksp")
}

fun AndroidSourceSet.proto(action: SourceDirectorySet.() -> Unit) {
    (this as? ExtensionAware)
        ?.extensions
        ?.getByName("proto")
        ?.let { it as? SourceDirectorySet }
        ?.apply(action)
}




android {
    namespace = "com.link.riderlink"

    signingConfigs {
        create("release") {
            storeFile = file("rider.jks")
            storePassword = "sunmedia"
            keyAlias = "rider"
            keyPassword = "sunmedia"
            enableV1Signing = true
            enableV2Signing = true
        }

        getByName("debug") {
            storeFile = file("rider.jks")
            storePassword = "sunmedia"
            keyAlias = "rider"
            keyPassword = "sunmedia"
            enableV1Signing = true
            enableV2Signing = true
        }
    }
    compileSdk = 35


    defaultConfig {
        minSdk = 24
        targetSdk = 35
        resourceConfigurations += listOf("zh-rCN", "en")

        ndk {
            //设置支持的SO库架构（开发者可以根据需要，选择一个或多个平台的so）
            //noinspection ChromeOsAbiSupport
            abiFilters += listOf("armeabi-v7a", "arm64-v8a")
        }
    }

    buildTypes {
        release {
            isDebuggable = false
            isJniDebuggable = false
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
        debug {
            isMinifyEnabled = false
            isShrinkResources = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    flavorDimensions += "version"
    productFlavors {
        create("amap") {
            dimension = "version"
            applicationId = "com.link.rider"
            targetSdk = 33
            versionCode = 7
            versionName = "1.1.1"
        }
        create("here") {
            dimension = "version"
            applicationId = "com.link.rider.global"
            targetSdk = 35
            versionCode = 2
            versionName = "1.0.1"
        }
    }

    androidComponents {
        onVariants { variant ->
            val versionName = variant.outputs.first().versionName.get()
            val appVersionValue = if (variant.buildType == "release") {
                versionName.toString()
            } else {
                "${versionName}_${currentBuildTimestamp}_${variant.buildType}"
            }
            val resKey = variant.makeResValueKey("string", "app_version")
            val resValue = ResValue(appVersionValue)
            variant.resValues.put(resKey, resValue)

            variant.outputs.forEach { output ->
                (output as? VariantOutputImpl)?.outputFileName?.set(
                    "Rider_${variant.flavorName}_${output.versionName.get()}_${currentBuildTimestamp}_${variant.buildType}.apk"
                )
            }
        }
    }

    buildFeatures {
        viewBinding = true
        buildConfig = true
    }


    sourceSets {
        getByName("main") {
            proto {
                srcDir("src/main/proto")
                include("**/*.proto")
            }
        }
        getByName("amap") {
            java.setSrcDirs(listOf("src/amap/java"))
            res.setSrcDirs(listOf("src/amap/res"))
            manifest.srcFile("src/amap/AndroidManifest.xml")
            assets.setSrcDirs(listOf("src/amap/assets"))
        }

        getByName("here") {
            java.setSrcDirs(listOf("src/here/java"))
            res.setSrcDirs(listOf("src/here/res"))
            manifest.srcFile("src/here/AndroidManifest.xml")
            assets.setSrcDirs(listOf("src/here/assets"))
        }
    }

    packaging {
        resources {
            excludes += "**/*.proto"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:2.6.1"
    }
    generateProtoTasks {
        all().forEach {
            it.builtins {
                create("java")
            }
        }
    }
}

dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))
    // 高德地图
    "amapImplementation"(
        fileTree(
            mapOf(
                "dir" to "libsamap",
                "include" to listOf("*.jar", "*.aar")
            )
        )
    )
    "amapImplementation"(libs.amap)
    "amapImplementation"(libs.amap.search)

    //here
    "hereImplementation"(
        fileTree(
            mapOf(
                "dir" to "libshere",
                "include" to listOf("*.jar", "*.aar")
            )
        )
    )


    // AndroidX
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)
    implementation(libs.androidx.work.runtime.ktx)
    implementation(libs.androidx.preference.ktx)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.datastore)
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)
    implementation(libs.google.material)
    implementation(libs.androidx.lifecycle.process)
    implementation(libs.androidx.startup)
    // Test
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.test.ext)
    androidTestImplementation(libs.espresso.core)
    // Others UI
    implementation(libs.android.autosize)
    implementation(libs.toast.utils)
    implementation(libs.dialogx)
    //permissions
    implementation(libs.xxpermissions)
    // protobuf
    compileOnly(libs.protoc)
    implementation(libs.protobuf.java)
    // leakcanary
    //debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.7'

    implementation(libs.roundedimageview)
    //implementation(libs.zxing.android.embedded)
    implementation(libs.eventbus)

}

// 自定义任务：同时构建多个flavor
tasks.register("buildMultipleFlavors") {
    dependsOn("assembleAmapDebug", "assembleHereDebug")
    group = "build"
    description = "构建多个flavor (amap和here的debug版本)"
}

// 自定义任务：同时构建所有flavor的release版本
tasks.register("buildAllFlavorsRelease") {
    dependsOn("assembleAmapRelease", "assembleHereRelease")
    group = "build"
    description = "构建所有flavor的release版本"
}