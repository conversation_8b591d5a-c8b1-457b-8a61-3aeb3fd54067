package com.link.riderlink.features.route

import com.link.riderlink.ui.theme.ThemeManager
import android.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amap.api.navi.model.NaviPoi
import com.link.rideramap.api.dto.MapCalcRouteResult
import com.link.riderlink.RiderLink
import com.link.riderlink.ui.extensions.SharedFlowEvents
import com.link.riderlink.ui.extensions.setEvent
import com.link.riderlink.ui.extensions.setState
import com.link.riderlink.ui.extensions.userPreferencesDataStore
import com.link.riderservice.api.callback.RiderServiceCallback
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.WifiStatus
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import me.jessyan.autosize.AutoSizeConfig
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import androidx.core.graphics.toColorInt
import com.link.rideramap.api.callback.MapCallback
import com.link.rideramap.api.callback.PlanRoutesListener
import com.link.rideramap.core.search.domain.entity.RouteData

class RouteViewModel : ViewModel() {

    private val _viewStates = MutableStateFlow(RouteViewState())

    val viewStates = _viewStates.asStateFlow()

    private val _viewEvents = SharedFlowEvents<RouteViewEvent>()

    val viewEvents = _viewEvents.asSharedFlow()

    private var shouldResumeNavigation = false

    fun dispatch(action: RouteViewAction) {
        when (action) {
            is RouteViewAction.StartNavi -> startNavi()
            is RouteViewAction.StopNavi -> {
                shouldResumeNavigation = false
                stopNavi()
            }

            is RouteViewAction.CalculateRideRoute -> {
                viewModelScope.launch {
                    val result = calculateRideRoute(action.from, action.to)
                    if (result.isEmpty()) {
                        _viewEvents.setEvent(RouteViewEvent.CalculateRouteFail("路线规划失败"))
                    } else {
                        _viewStates.setState {
                            copy(routesList = result)
                        }
                    }
                }
            }
        }
    }

    private fun stopNavi() {
        RiderLink.instance.stopNavi()
    }

    private suspend fun calculateRideRoute(
        startPoint: NaviPoi,
        endPoint: NaviPoi
    ): List<RouteData> {
        return suspendCoroutine { routeCalculationContinuation ->
            RiderLink.instance.calculateDriveRoute(
                startPoint,
                endPoint,
                object : PlanRoutesListener {
                    override fun onCalculateRouteSuccess(routeResult: MapCalcRouteResult) {
                        if (routeResult.errorCode != 0) {
                            routeCalculationContinuation.resume(arrayListOf())
                        } else {
                            if (routeResult.routeList.isEmpty()) {
                                routeCalculationContinuation.resume(arrayListOf())
                            } else {
                                routeCalculationContinuation.resume(routeResult.routeList)
                            }
                        }
                    }

                    override fun onCalculateRouteFailure(routeResult: MapCalcRouteResult) {
                        routeCalculationContinuation.resume(arrayListOf())
                    }
                })
        }
    }

    private fun startNavi() {
        RiderLink.instance.startNavi()
    }

    private val navigationCallback = object : MapCallback() {
        override fun onStartNavi() {
            super.onStartNavi()
            _viewStates.setState {
                copy(naviState = NaviState.STARTED)
            }
            _viewStates.setState {
                copy(onNavi = true)
            }
            ThemeManager.showBar(true, "#040507".toColorInt())
        }

        override fun onStopNavi() {
            super.onStopNavi()
            shouldResumeNavigation = false
            _viewStates.setState {
                copy(naviState = NaviState.STOPPED)
            }
            _viewStates.setState {
                copy(onNavi = false)
            }
            ThemeManager.showBar(
                true, ThemeManager.autoChangeInt(
                    Color.TRANSPARENT,
                    "#2A3042".toColorInt()
                )
            )
        }

        override fun onEndEmulatorNavi() {
            super.onEndEmulatorNavi()
            shouldResumeNavigation = false
            _viewStates.setState {
                copy(naviState = NaviState.DESTINATION)
            }
        }

        override fun onArriveDestination() {
            super.onArriveDestination()
            shouldResumeNavigation = false
            _viewStates.setState {
                copy(naviState = NaviState.DESTINATION)
            }
        }

        override fun changeMap(mapDisplayMode: Int) {
            super.changeMap(mapDisplayMode)
            _viewStates.setState {
                copy(mapState = mapDisplayMode)
            }
        }
    }


    private val riderServiceConnectionCallback = object : RiderServiceCallback() {
        override fun onConnectStatusChange(connectionStatus: Connection) {
            _viewStates.setState {
                copy(status = connectionStatus)
            }

            if (connectionStatus.btStatus is BleStatus.DeviceDisconnected) {
                if (_viewStates.value.naviState == NaviState.STARTED) {
                    shouldResumeNavigation = true
                    return
                }
            }

            if (connectionStatus.wifiStatus is WifiStatus.DeviceDisconnected) {
                if (_viewStates.value.naviState == NaviState.STARTED) {
                    shouldResumeNavigation = true
                    return
                }
            }
        }

        override fun onClusterReady() {
            super.onClusterReady()
            if (shouldResumeNavigation) {
                startNavi()
                return
            }
        }

        override fun onVideoChannelReady() {
            super.onVideoChannelReady()
            if (shouldResumeNavigation) {
                startNavi()
            }
        }

        override fun changeMap(mapDisplayMode: Int) {
            super.changeMap(mapDisplayMode)
            _viewStates.setState {
                copy(mapState = mapDisplayMode)
            }
        }
    }


    override fun onCleared() {
        super.onCleared()
        RiderLink.instance.setIsNaviPage(false)
        RiderLink.instance.removeConnectCallback(riderServiceConnectionCallback)
        RiderLink.instance.removeMapCallback(navigationCallback)
    }

    init {
        RiderLink.instance.setIsNaviPage(true)
        RiderLink.instance.addMapCallback(navigationCallback)
        RiderLink.instance.addConnectCallback(riderServiceConnectionCallback)
        viewModelScope.launch {
            val isScreenNavigationSupported =
                AutoSizeConfig.getInstance().application.userPreferencesDataStore.data.map {
                    it.isSupportScreenNavi
                }.first()
            _viewStates.setState {
                copy(isSupportScreening = isScreenNavigationSupported)
            }
            RiderLink.instance.getConnectStatus().let {
                _viewStates.setState {
                    copy(status = it)
                }
            }
        }
    }

    companion object {
        private const val TAG = "RouteViewModel"
    }
}