package com.link.riderlink.features.home

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.link.rideramap.api.callback.MapCallback
import com.link.riderlink.RiderLink
import com.link.riderlink.ui.extensions.SharedFlowEvents
import com.link.riderlink.ui.extensions.collectWithScope
import com.link.riderlink.ui.extensions.setEvent
import com.link.riderlink.ui.extensions.setState
import com.link.riderlink.ui.extensions.updateUserPreferences
import com.link.riderlink.ui.extensions.userPreferencesDataStore
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.callback.RiderServiceCallback
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.RiderServiceConfig
import com.link.riderservice.api.dto.WeatherInfo
import com.link.riderservice.api.dto.WifiStatus
import com.link.riderservice.core.extensions.coroutines.mainScope
import com.link.riderservice.feature.connection.ble.BleDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import me.jessyan.autosize.AutoSizeConfig

class HomeViewModel : ViewModel() {
    private val _viewStates = MutableStateFlow(HomeViewState())

    val viewStates = _viewStates.asStateFlow()

    private val _viewEvents = SharedFlowEvents<HomeViewEvent>()

    val viewEvents = _viewEvents.asSharedFlow()


    fun dispatch(viewAction: HomeViewAction) {
        when (viewAction) {
            is HomeViewAction.StartLocation -> {
                updateLocation()
            }

            is HomeViewAction.Disconnect -> {
                RiderLink.instance.disconnect()
                RiderService.instance.deleteConfig()
                changeIdleText()
            }

            is HomeViewAction.StartScan -> {
                RiderLink.instance.startBleScan(viewAction.shouldAutoConnect)
            }

            is HomeViewAction.Mirror -> {
                if (RiderLink.instance.getConnectStatus().btStatus is BleStatus.DeviceConnected && RiderLink.instance.getConnectStatus().wifiStatus is WifiStatus.DeviceConnected) {
                    if (RiderLink.instance.isMirroring()) {
                        RiderLink.instance.stopMirror()
                    } else {
                        RiderLink.instance.startMirror(viewAction.launcher)
                    }
                }
            }

            is HomeViewAction.BleItemClicked -> {
                RiderLink.instance.connect(viewAction.bleDevice)
            }

            is HomeViewAction.SingleDisconnect -> {
                RiderLink.instance.disconnect()
            }

        }
    }

    private fun updateLocation() {
        viewModelScope.launch {
            val locationInfo = RiderLink.instance.startLbsLocation()
            _viewEvents.setEvent(HomeViewEvent.UpdateLocation(locationInfo))
            if (locationInfo.altitude != 0.0) {
                _viewStates.setState {
                    copy(geographyInfo = geographyInfo.copy(altitude = locationInfo.altitude.toFloat()))
                }
            }
        }
    }

    private fun changeIdleText() {
        viewModelScope.launch {
            _viewEvents.setEvent(HomeViewEvent.ChangeIdleText)
        }
    }

    private fun refreshGeographyInfo() {
        viewModelScope.launch(Dispatchers.IO) {
            val weatherData = RiderLink.instance.getAMapWeatherInfo()
            _viewStates.setState {
                copy(geographyInfo = geographyInfo.copy(weatherData))
            }
        }
    }

    private val mMapCallback = object : MapCallback() {
        override fun changeMap(type: Int) {
            super.changeMap(type)
            _viewStates.setState {
                copy(mapState = type)
            }
        }
    }

    private val connectCallback = object : RiderServiceCallback() {
        override fun onConnectStatusChange(notificationStatus: Connection) {
            _viewStates.setState {
                copy(status = notificationStatus)
            }
            viewModelScope.launch {
                _viewEvents.setEvent(HomeViewEvent.ConnectStatusChange(notificationStatus))
            }
        }

        override fun onConfigChange(riderServiceConfig: RiderServiceConfig) {
            super.onConfigChange(riderServiceConfig)
            viewModelScope.launch {
                AutoSizeConfig.getInstance().application.updateUserPreferences(riderServiceConfig)
            }
        }

        override fun onRequestWeatherInfo() {
            super.onRequestWeatherInfo()
            refreshGeographyInfo()
            val geographyInfo = _viewStates.value.geographyInfo
            RiderLink.instance.sendMessageToRiderService(
                WeatherInfo(
                    wea = geographyInfo.weather.weather,
                    tem = geographyInfo.weather.temperature,
                    humidity = geographyInfo.weather.humidity,
                    pressure = "",
                    altitude = geographyInfo.altitude.toString()
                )
            )
        }

        override fun onWifiState(isWifiOpened: Boolean) {
            super.onWifiState(isWifiOpened)
            if (isWifiOpened) {
                RiderLink.instance.onWifiOpened()
            }
            _viewStates.setState {
                copy(isWifiOpen = isWifiOpened)
            }

        }

        override fun onMirrorStart() {
            super.onMirrorStart()
            _viewStates.setState {
                copy(isMirror = true)
            }
        }

        override fun onMirrorStop() {
            super.onMirrorStop()
            _viewStates.setState {
                copy(isMirror = false)
            }
        }

        override fun changeMap(mapType: Int) {
            super.changeMap(mapType)
            _viewStates.setState {
                copy(mapState = mapType)
            }
        }

        override fun onScanResult(bleDevices: List<BleDevice>) {
            Log.d(TAG, "onScanResult")
            _viewStates.setState {
                copy(bleList = bleDevices.toList())
            }
        }

        override fun onScanning() {
            _viewStates.setState {
                copy(isScanning = true)
            }
        }

        override fun onScanFinish() {
            _viewStates.setState {
                copy(isScanning = false)
            }
        }
    }

    init {
        RiderLink.instance.addConnectCallback(connectCallback)
        RiderLink.instance.addMapCallback(mMapCallback)
        AutoSizeConfig.getInstance().application.userPreferencesDataStore.data.collectWithScope(
            mainScope
        ) {
            val config = RiderServiceConfig(
                isSupportDvr = it.isSupportDvr,
                isSupportNavi = it.isSupportNavi,
                isSupportScreenNavi = it.isSupportScreenNavi,
                isSupportWeather = it.isSupportWeather,
                isSupportNotification = it.isSupportNotification,
                isSupportCircularScreen = it.isSupportCircularScreen,
                isSupportCruise = it.isSupportCruise,
                isSupportMirror = true
            )
            RiderLink.instance.setRiderServiceConfig(config)
            if (it.isInitialized) {
                _viewStates.setState {
                    copy(config = config)
                }
            }
        }
        RiderLink.instance.getConnectStatus().let {
            _viewStates.setState {
                copy(status = it)
            }
        }
        RiderLink.instance.isMirroring().let {
            _viewStates.setState {
                copy(isMirror = it)
            }
        }
        refreshGeographyInfo()
    }

    companion object {
        private const val TAG = "HomeViewModel"
    }
}