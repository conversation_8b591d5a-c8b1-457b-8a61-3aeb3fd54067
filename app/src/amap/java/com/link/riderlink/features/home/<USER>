package com.link.riderlink.features.home

import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import com.link.rideramap.core.location.domain.entity.LocationInfo
import com.link.riderservice.api.dto.RiderServiceConfig
import com.link.riderservice.api.dto.Connection
import com.link.riderlink.utils.GeographyInfo
import com.link.riderservice.feature.connection.ble.BleDevice

data class HomeViewState(
    val geographyInfo: GeographyInfo = GeographyInfo(),
    val status: Connection = Connection(),
    val mapState: Int = 0,
    val config: RiderServiceConfig = RiderServiceConfig(
        isSupportDvr = false,
        isSupportNavi = false,
        isSupportScreenNavi = false,
        isSupportWeather = false,
        isSupportNotification = false,
        isSupportCircularScreen = false,
        isSupportCruise = false,
        isSupportMirror = false
    ),
    val isMirror: Boolean = false,
    val isWifiOpen: Boolean = false,
    val bleList: List<BleDevice> = emptyList(),
    val isScanning: Boolean = false
)

sealed class HomeViewEvent {
    data class UpdateLocation(val locationInfo: LocationInfo) : HomeViewEvent()
    data class ConnectStatusChange(val status: Connection) : HomeViewEvent()
    object ChangeIdleText : HomeViewEvent()

}


sealed class HomeViewAction {
    object Disconnect : HomeViewAction()
    object StartLocation : HomeViewAction()
    data class StartScan(val shouldAutoConnect: Boolean = true) : HomeViewAction()
    data class Mirror(val launcher: ActivityResultLauncher<Intent>) : HomeViewAction()
    data class BleItemClicked(val bleDevice: BleDevice) : HomeViewAction()
    object SingleDisconnect : HomeViewAction()
}

