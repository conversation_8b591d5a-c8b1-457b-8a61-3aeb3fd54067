package com.link.riderlink.features.route

import com.amap.api.navi.model.NaviPoi
import com.link.rideramap.core.search.domain.entity.RouteData
import com.link.riderservice.api.dto.Connection

sealed class NaviState {
    object NONE : NaviState()
    object STARTED : NaviState()
    object STOPPED : NaviState()
    object DESTINATION : NaviState()
}

data class RouteViewState(
    val naviState: NaviState = NaviState.NONE,
    val mapState: Int = 0,
    val routesList: List<RouteData> = emptyList(),
    val status: Connection = Connection(),
    val isSupportScreening: Boolean = true,
    val onNavi: Boolean = false
)

sealed class RouteViewEvent {
    data class CalculateRouteFail(val message: String) : RouteViewEvent()
}


sealed class RouteViewAction {
    object StopNavi : RouteViewAction()
    object StartNavi : RouteViewAction()
    data class CalculateRideRoute(val from: NaviPoi, val to: NaviPoi) : RouteViewAction()
}