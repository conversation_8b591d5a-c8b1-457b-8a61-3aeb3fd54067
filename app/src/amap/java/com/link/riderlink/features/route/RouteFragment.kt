package com.link.riderlink.features.route

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.annotation.ColorInt
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.amap.api.maps.model.LatLng
import com.amap.api.navi.model.NaviPoi
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.ToastUtils
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.databinding.FragmentRouteBinding
import com.link.riderlink.features.search.SearchDialogFragment
import com.link.riderlink.ui.extensions.changeAppearanceStatusBars
import com.link.riderlink.ui.extensions.changeSystemBarColorActually
import com.link.riderlink.ui.extensions.getDestination
import com.link.riderlink.ui.extensions.observeEvent
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.extensions.saveDestination
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.utils.common.Destination
import com.link.riderlink.utils.system.locationPermissions
import com.link.riderservice.api.dto.WifiStatus
import kotlinx.coroutines.launch

/**
 * 路线规划Fragment
 * 负责处理起点终点选择、路线计算、导航功能
 */
class RouteFragment : Fragment() {

    private val routeViewModel: RouteViewModel by viewModels()
    private var _binding: FragmentRouteBinding? = null
    private lateinit var binding: FragmentRouteBinding

    // 自定义起点和终点，用户手动选择后会一直使用，直到Fragment销毁
    private var customStartPoint: SearchAddress? = null
    private var customEndPoint: SearchAddress? = null

    private val themeChangeListener = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }
    
    override fun onAttach(context: Context) {
        super.onAttach(context)
        setupBackPressHandler()
    }

    override fun onCreateView(
        inflater: LayoutInflater, 
        container: ViewGroup?, 
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRouteBinding.inflate(inflater, container, false)
        binding = _binding!!
        setupInitialSystemBarColor()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupWindowInsets()
        setupUI()
        setupViewModelObservers()
        initializeRoute()
        initTheme()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cleanup()
    }
    
    private fun setupBackPressHandler() {
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            if (routeViewModel.viewStates.value.naviState != NaviState.STARTED) {
                popBackStack()
            } else {
                binding.mapRoute.showExitBar()
            }
        }
    }

    private fun setupInitialSystemBarColor() {
        changeSystemBarColor(
            isNavigationMode = false,
            statusBarNightColor = getColorCompat(R.color.route_status_bar_night),
            navigationBarNightColor = getColorCompat(R.color.route_navigation_bar_night)
        )
    }

    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { view, windowInsets ->
            val systemBars = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
            view.updatePadding(
                top = systemBars.top,
                bottom = systemBars.bottom
            )
            windowInsets
        }
    }

    private fun setupUI() {
        setupMapListeners()
        ThemeManager.registerThemeChangeListener(themeChangeListener)
    }

    private fun setupMapListeners() {
        // 导航按钮监听
        binding.mapRoute.setNavigateButtonListener {
            Log.d(TAG, "startNavi")
            changeSystemBarColor(
                isNavigationMode = true,
                statusBarColor = getColorCompat(R.color.route_navi_status_bar),
                statusBarNightColor = getColorCompat(R.color.route_navi_status_bar_night),
                navigationBarNightColor = getColorCompat(R.color.route_navi_navigation_bar_night)
            )
            startNavi()
        }

        // 停止导航按钮监听
        binding.mapRoute.setStopNaviButtonListener {
            changeSystemBarColor(
                isNavigationMode = false,
                statusBarNightColor = getColorCompat(R.color.route_status_bar_night),
                navigationBarNightColor = getColorCompat(R.color.route_navigation_bar_night)
            )
            routeViewModel.dispatch(RouteViewAction.StopNavi)
        }

        // 起点选择监听
        binding.mapRoute.setStartListener {
            showStartPointSearchDialog()
        }

        // 终点选择监听
        binding.mapRoute.setEndListener {
            showEndPointSearchDialog()
        }

        // 返回按钮监听
        binding.mapRoute.setBackButtonListener {
            popBackStack()
        }

        binding.mapRoute.setNavi()
    }
    
    private fun setupViewModelObservers() {
        observeViewStates()
        observeViewEvents()
    }

    private fun observeViewStates() {
        routeViewModel.viewStates.let { states ->
            // 观察连接状态
            states.observeState(viewLifecycleOwner, RouteViewState::status) {
                binding.mapRoute.setNavigateEnable(it.wifiStatus is WifiStatus.DeviceConnected)
            }

            // 观察地图状态
            states.observeState(viewLifecycleOwner, RouteViewState::mapState) {
                try {
                    binding.mapRoute.changeMap()
                } catch (e: Exception) {
                    Log.e(TAG, "地图状态变化处理失败: $e")
                }
            }

            // 观察导航状态
            states.observeState(viewLifecycleOwner, RouteViewState::naviState) {
                handleNavigationStateChange(it)
            }

            // 观察路线列表
            states.observeState(
                viewLifecycleOwner,
                RouteViewState::routesList,
                Lifecycle.State.CREATED
            ) {
                binding.mapRoute.setRouteList(it)
            }
        }
    }

    private fun observeViewEvents() {
        routeViewModel.viewEvents.observeEvent(viewLifecycleOwner) {
            when (it) {
                is RouteViewEvent.CalculateRouteFail -> {
                    ToastUtils.show(it.message)
                    binding.mapRoute.setNavigateEnable(false)
                }
            }
        }
    }
    
    private fun initializeRoute() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                loadAndCalculateInitialRoute()
            }
        }
    }

    private suspend fun loadAndCalculateInitialRoute() {
        // 获取当前使用的起点和终点
        val startNaviPoi = getCurrentStartNaviPoi()
        val endPoint = getCurrentEndPoint()
        
        logDestinationInfo(endPoint as? Destination)
        
        // 更新UI显示
        binding.mapRoute.setStart(startNaviPoi.name)
        when (endPoint) {
            is SearchAddress -> binding.mapRoute.setDestination(endPoint.name)
            is Destination -> binding.mapRoute.setDestination(endPoint.address)
            else -> binding.mapRoute.setDestination(null)
        }
        
        // 计算初始路线
        if (endPoint != null) {
            val endNaviPoi = createNaviPoiFromEndPoint(endPoint)
            calculateRoute(startNaviPoi, endNaviPoi, "初始化路线计算")
        }
    }
    
    private fun showStartPointSearchDialog() {
        val searchDialog = SearchDialogFragment { selectedAddress ->
            updateStartPoint(selectedAddress)
        }
        searchDialog.show(childFragmentManager, "SearchStartDialog")
    }

    private fun showEndPointSearchDialog() {
        val searchDialog = SearchDialogFragment { selectedAddress ->
            updateEndPoint(selectedAddress)
        }
        searchDialog.show(childFragmentManager, "SearchEndDialog")
    }
    
    /**
     * 更新起点信息并重新计算路线
     */
    private fun updateStartPoint(selectedAddress: SearchAddress) {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                // 保存自定义起点
                customStartPoint = selectedAddress
                
                // 更新起点显示
                binding.mapRoute.setStart(selectedAddress.name)
                
                // 获取当前使用的终点信息并重新计算路线
                val endPoint = getCurrentEndPoint()
                if (endPoint != null) {
                    val startNaviPoi = createNaviPoi(selectedAddress)
                    val endNaviPoi = createNaviPoiFromEndPoint(endPoint)
                    calculateRoute(startNaviPoi, endNaviPoi, "重新计算路线: 起点=${selectedAddress.name}")
                } else {
                    ToastUtils.show("未获取到终点信息")
                }
            } catch (e: Exception) {
                Log.e(TAG, "更新起点失败: ${e.message}")
                ToastUtils.show("更新起点失败")
            }
        }
    }

    /**
     * 更新终点信息并重新计算路线
     */
    private fun updateEndPoint(selectedAddress: SearchAddress) {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                // 保存自定义终点
                customEndPoint = selectedAddress
                
                // 保存新的目的地信息
                saveNewDestination(selectedAddress)

                // 更新终点显示
                binding.mapRoute.setDestination(selectedAddress.name)

                // 获取当前使用的起点信息并重新计算路线
                val startNaviPoi = getCurrentStartNaviPoi()
                val endNaviPoi = createNaviPoi(selectedAddress)
                calculateRoute(startNaviPoi, endNaviPoi, "重新计算路线: 终点=${selectedAddress.name}")
            } catch (e: Exception) {
                Log.e(TAG, "更新终点失败: ${e.message}")
                ToastUtils.show("更新终点失败")
            }
        }
    }

    private fun startNavi() {
        if (XXPermissions.isGranted(requireContext(), Permission.ACCESS_BACKGROUND_LOCATION)) {
            routeViewModel.dispatch(RouteViewAction.StartNavi)
        } else {
            requestLocationPermissions()
        }
    }

    private fun requestLocationPermissions() {
        XXPermissions.with(requireContext())
            .permission(locationPermissions)
            .permission(Permission.ACCESS_BACKGROUND_LOCATION)
            .request(object : OnPermissionCallback {
                override fun onDenied(permissions: MutableList<String>, doNotAskAgain: Boolean) {
                    super.onDenied(permissions, doNotAskAgain)
                    XXPermissions.startPermissionActivity(requireContext(), permissions)
                }

                override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                    if (allGranted) {
                        routeViewModel.dispatch(RouteViewAction.StartNavi)
                    }
                }
            })
    }

    private fun handleNavigationStateChange(naviState: NaviState) {
        when (naviState) {
            NaviState.STOPPED, NaviState.DESTINATION -> {
                binding.mapRoute.stopNaviAnimation()
            }
            NaviState.STARTED -> {
                binding.mapRoute.startNaviAnimation()
            }
            else -> {}
        }
    }
    
    /**
     * 获取当前起点的NaviPoi
     * 优先使用用户自定义的起点，否则使用当前位置
     */
    private suspend fun getCurrentStartNaviPoi(): NaviPoi {
        // 如果用户设置了自定义起点，优先使用
        customStartPoint?.let { customStart ->
            return createNaviPoi(customStart)
        }
        
        // 否则使用当前位置作为起点
        val currentStart = RiderLink.instance.getLbsLocation()
        val startAddress = if (TextUtils.isEmpty(currentStart.address)) {
            currentStart.province.plus(currentStart.city).plus(currentStart.district)
        } else {
            currentStart.address
        }
        
        return NaviPoi(
            startAddress,
            LatLng(currentStart.latitude, currentStart.longitude),
            startAddress
        )
    }

    /**
     * 从SearchAddress创建NaviPoi
     */
    private fun createNaviPoi(searchAddress: SearchAddress): NaviPoi {
        return NaviPoi(
            searchAddress.name,
            LatLng(
                searchAddress.point_latitude.toDouble(),
                searchAddress.point_longitude.toDouble()
            ),
            searchAddress.name
        )
    }

    /**
     * 从Destination创建NaviPoi
     */
    private fun createNaviPoi(destination: Destination): NaviPoi {
        return NaviPoi(
            destination.address,
            LatLng(destination.latitude, destination.longitude),
            destination.address
        )
    }
    
    /**
     * 获取当前使用的终点
     * 优先使用用户自定义的终点，否则使用系统保存的目的地
     */
    private fun getCurrentEndPoint(): Any? {
        return customEndPoint ?: context?.getDestination()
    }
    
    /**
     * 从终点对象创建NaviPoi（支持SearchAddress和Destination）
     */
    private fun createNaviPoiFromEndPoint(endPoint: Any): NaviPoi {
        return when (endPoint) {
            is SearchAddress -> createNaviPoi(endPoint)
            is Destination -> createNaviPoi(endPoint)
            else -> throw IllegalArgumentException("Unsupported end point type: ${endPoint::class.java}")
        }
    }
    
    /**
     * 计算路线的通用方法
     */
    private fun calculateRoute(startNaviPoi: NaviPoi, endNaviPoi: NaviPoi, logMessage: String) {
        try {
            routeViewModel.dispatch(
                RouteViewAction.CalculateRideRoute(startNaviPoi, endNaviPoi)
            )
            Log.d(TAG, logMessage)
        } catch (e: Exception) {
            Log.e(TAG, "路线计算失败: ${e.message}")
            ToastUtils.show("路线计算失败")
        }
    }
    
    private fun initTheme() {
        val currentThemeResProvider = { resId: Int ->
            ThemeManager.getCurrentThemeRes(requireContext(), resId)
        }
        val autoChangingStringProvider = { day: String, night: String ->
            ThemeManager.autoChangeStr(day, night)
        }
        val nightModeChecker = {
            ThemeManager.isNightMode(requireContext())
        }
        binding.mapRoute.initTheme(
            currentThemeResProvider,
            autoChangingStringProvider,
            nightModeChecker
        )
    }
    
    private fun changeSystemBarColor(
        isNavigationMode: Boolean = false,
        @ColorInt statusBarColor: Int = Color.WHITE,
        @ColorInt navigationBarColor: Int = Color.WHITE,
        @ColorInt statusBarNightColor: Int,
        @ColorInt navigationBarNightColor: Int
    ) {
        val isDay = ThemeManager.themeMode == ThemeManager.ThemeMode.DAY
        val currentStatusBarColor = if (isDay) statusBarColor else statusBarNightColor
        val currentNavigationBarColor = if (isDay) navigationBarColor else navigationBarNightColor
        changeSystemBarColorActually(currentStatusBarColor, currentNavigationBarColor)
        changeAppearanceStatusBars(isNavigationMode)
    }
    
    private fun getColorCompat(colorResourceId: Int) = requireContext().getColor(colorResourceId)

    private suspend fun saveNewDestination(selectedAddress: SearchAddress) {
        val newDestination = Destination(
            latitude = selectedAddress.point_latitude.toDouble(),
            longitude = selectedAddress.point_longitude.toDouble(),
            address = selectedAddress.name,
            district = selectedAddress.district
        )
        context?.saveDestination(newDestination)
    }

    private fun logDestinationInfo(destination: Destination?) {
        Log.e(TAG, "search: ${destination?.latitude}")
        Log.e(TAG, "search: ${destination?.longitude}")
        Log.e(TAG, "search: ${destination?.address}")
        Log.e(TAG, "search: ${destination?.district}")
    }

    private fun cleanup() {
        changeSystemBarColorActually(Color.TRANSPARENT, Color.TRANSPARENT)
        ThemeManager.unregisterThemeChangeListener(themeChangeListener)
        
        // 清除自定义的起点和终点
        customStartPoint = null
        customEndPoint = null
        
        _binding = null
    }
    
    companion object {
        private const val TAG = "RouteSelectFragment"
    }
}