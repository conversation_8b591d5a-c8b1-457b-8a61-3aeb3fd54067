package com.link.riderlink.features.search

import androidx.lifecycle.viewModelScope
import com.link.rideramap.api.dto.PoiAddress
import com.link.rideramap.api.dto.SearchResult
import com.link.riderlink.RiderLink
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.core.shared.search.ui.BaseSearchViewAction
import com.link.riderlink.core.shared.search.ui.BaseSearchViewModel
import com.link.riderlink.ui.extensions.setState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 高德地图搜索ViewModel实现
 * 继承BaseSearchViewModel，只需实现具体的搜索API调用
 */
class SearchViewModel : BaseSearchViewModel() {

    // 高德地图特有的POI搜索结果状态
    private val _poiResultData = MutableStateFlow<SearchResult?>(null)
    val poiResultData = _poiResultData.asStateFlow()

    /**
     * 处理高德地图特有的动作（包括基础动作和POI搜索）
     */
    fun dispatch(viewAction: SearchViewAction) {
        when (viewAction) {
            is SearchViewAction.Search -> super.dispatch(BaseSearchViewAction.Search(viewAction.keyword))
            is SearchViewAction.InsertHistory -> super.dispatch(
                BaseSearchViewAction.InsertHistory(
                    viewAction.searchAddress
                )
            )

            is SearchViewAction.DeleteHistory -> super.dispatch(
                BaseSearchViewAction.DeleteHistory(
                    viewAction.searchAddress
                )
            )

            is SearchViewAction.LoadHistories -> super.dispatch(BaseSearchViewAction.LoadHistories)
            is SearchViewAction.SearchPOI -> searchPOI(viewAction.poiAddress)
        }
    }

    /**
     * 实现基类的抽象方法：执行高德地图搜索API调用
     */
    override suspend fun performSearchApi(keyword: String): List<SearchAddress> {
        val result = RiderLink.instance.search(keyword)
        return result.searchList?.map {
            SearchAddress(
                ad_code = it.adCode,
                district = it.district,
                name = it.name,
                poi_id = it.poiId,
                point_latitude = it.latitude.toString(),
                point_longitude = it.longitude.toString(),
            )
        } ?: emptyList()
    }

    /**
     * POI搜索（高德地图特有功能）
     */
    private fun searchPOI(poiAddress: PoiAddress) {
        viewModelScope.launch {
            try {
                val result = RiderLink.instance.searchPOI(poiAddress)
                _poiResultData.setState { result }
            } catch (e: Exception) {
                // 错误处理
            }
        }
    }
}