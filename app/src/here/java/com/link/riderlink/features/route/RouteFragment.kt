package com.link.riderlink.features.route

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.annotation.ColorInt
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.here.sdk.core.GeoCoordinates
import com.here.sdk.routing.Route
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.ToastUtils
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.core.database.model.SearchAddress
import com.link.riderlink.databinding.FragmentRouteBinding
import com.link.riderlink.features.search.SearchDialogFragment
import com.link.riderlink.ui.extensions.changeAppearanceStatusBars
import com.link.riderlink.ui.extensions.changeSystemBarColorActually
import com.link.riderlink.ui.extensions.getDestination
import com.link.riderlink.ui.extensions.observeEvent
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.extensions.saveDestination
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.utils.common.Destination
import com.link.riderlink.utils.system.locationPermissions
import com.link.riderservice.api.dto.WifiStatus
import com.link.riderservice.core.utils.logging.logD
import kotlinx.coroutines.launch


class RouteFragment : Fragment() {
    private val routeViewModel: RouteViewModel by viewModels()
    private var _binding: FragmentRouteBinding? = null
    private val binding get() = _binding!!

    private var startCoordinates: GeoCoordinates? = null
    private var endCoordinates: GeoCoordinates? = null

    // 自定义起点和终点，用户手动选择后会一直使用，直到Fragment销毁
    private var customStartPoint: SearchAddress? = null
    private var customEndPoint: SearchAddress? = null

    private val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            if (routeViewModel.viewStates.value.navigationState != NaviState.STARTED) {
                popBackStack()
            } else {
                binding.mapRoute.showExitBar()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRouteBinding.inflate(inflater, container, false)
        changeSystemBarColor(
            isNavi = false,
            statusBarNightColor = getColorCompat(R.color.route_status_bar_night),
            navigationBarNightColor = getColorCompat(R.color.route_navigation_bar_night)
        )
        return binding.root
    }

    private fun getColorCompat(colorResourceId: Int) = requireContext().getColor(colorResourceId)

    private fun changeSystemBarColor(
        isNavi: Boolean = false,
        @ColorInt statusBarColor: Int = Color.WHITE,
        @ColorInt navigationBarColor: Int = Color.WHITE,
        @ColorInt statusBarNightColor: Int,
        @ColorInt navigationBarNightColor: Int
    ) {
        val isDay = ThemeManager.themeMode == ThemeManager.ThemeMode.DAY
        val currentStatusBarColor = if (isDay) statusBarColor else statusBarNightColor
        val currentNavigationBarColor = if (isDay) navigationBarColor else navigationBarNightColor
        changeSystemBarColorActually(currentStatusBarColor, currentNavigationBarColor)
        changeAppearanceStatusBars(isNavi)
    }

    private fun init() = with(binding.mapRoute) {
        setNavigateButtonListener {
            changeSystemBarColor(
                isNavi = true,
                statusBarColor = getColorCompat(R.color.route_navi_status_bar),
                statusBarNightColor = getColorCompat(R.color.route_navi_status_bar_night),
                navigationBarNightColor = getColorCompat(R.color.route_navi_navigation_bar_night)
            )
            startNavi()
        }
        setStopNaviButtonListener {
            changeSystemBarColor(
                isNavi = false,
                statusBarNightColor = getColorCompat(R.color.route_status_bar_night),
                navigationBarNightColor = getColorCompat(R.color.route_navigation_bar_night)
            )
            routeViewModel.dispatch(RouteViewAction.StopNavi)
        }
        setStartListener {
            val searchDialog = SearchDialogFragment { selectedAddress ->
                updateStartPoint(selectedAddress)
            }
            searchDialog.show(childFragmentManager, "SearchStartDialog")
        }

        setEndListener {
            val searchDialog = SearchDialogFragment { selectedAddress ->
                updateEndPoint(selectedAddress)
            }
            searchDialog.show(childFragmentManager, "SearchEndDialog")
        }

        setBackButtonListener { popBackStack() }
        setNavi()
    }

    override fun onViewCreated(createdView: View, savedInstanceState: Bundle?) {
        super.onViewCreated(createdView, savedInstanceState)
        binding.root.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, windowInsets ->
            val systemBars = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.updatePadding(
                top = systemBars.top,
                bottom = systemBars.bottom
            )
            windowInsets
        }
        init()
        observeViewModel()
        observeEvents()
        fetchRouteData()
        initTheme()
    }

    private fun observeViewModel() = with(routeViewModel.viewStates) {
        observeState(viewLifecycleOwner, RouteViewState::connectionStatus) { connectionStatus ->
            logD("connectionStatus: $connectionStatus")
            _binding?.mapRoute?.setNavigateEnable(connectionStatus.wifiStatus is WifiStatus.DeviceConnected)
        }

        observeState(viewLifecycleOwner, RouteViewState::navigationState) { naviState ->
            when (naviState) {
                NaviState.STOPPED, NaviState.DESTINATION -> {
                    binding.mapRoute.stopNaviAnimation()
                    changeSystemBarColor(
                        isNavi = false,
                        statusBarNightColor = getColorCompat(R.color.route_status_bar_night),
                        navigationBarNightColor = getColorCompat(R.color.route_navigation_bar_night)
                    )
                }

                NaviState.STARTED -> binding.mapRoute.startNaviAnimation()
                else -> {}
            }
        }

        observeState(
            viewLifecycleOwner,
            RouteViewState::routes,
            Lifecycle.State.CREATED
        ) { routes ->
            if (routes.isNotEmpty()) {
                startCoordinates?.let { start ->
                    endCoordinates?.let { end ->
                        _binding?.mapRoute?.setNavigateEnable(true)
                        binding.mapRoute.setRouteList(
                            start,
                            end,
                            routes as List<Route>
                        )
                    }
                } ?: run {
                    Log.e(TAG, "setRouteList: 起点或终点坐标为空")
                }
            }
        }
    }

    private fun observeEvents() {
        routeViewModel.viewEvents.observeEvent(viewLifecycleOwner) {

            when (it) {
                is RouteViewEvent.CalculateRouteFail -> {
                    ToastUtils.show(it.errorMessage)
                    binding.mapRoute.setNavigateEnable(false)
                }

                is RouteViewEvent.NaviDisplayTypeChanged -> {
                    changeSystemBarColor(
                        isNavi = true,
                        statusBarColor = getColorCompat(R.color.route_navi_status_bar),
                        statusBarNightColor = getColorCompat(R.color.route_navi_status_bar_night),
                        navigationBarNightColor = getColorCompat(R.color.route_navi_navigation_bar_night)
                    )
                }
            }
        }
    }

    private fun fetchRouteData() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                try {
                    // 获取当前使用的起点和终点
                    val startGeoCoordinates = getCurrentStartGeoCoordinates()
                    val endPoint = getCurrentEndPoint()
                    
                    if (endPoint == null) {
                        Log.e(TAG, "fetchRouteData: destination is null")
                        ToastUtils.show("未获取到目的地信息")
                        return@repeatOnLifecycle
                    }

                    val endGeoCoordinates = createGeoCoordinatesFromEndPoint(endPoint)

                    if (startGeoCoordinates != null) {
                        startCoordinates = startGeoCoordinates
                        endCoordinates = endGeoCoordinates

                        _binding?.mapRoute?.apply {
                            setStart(getStartDisplayName())
                            setDestination(when (endPoint) {
                                is SearchAddress -> endPoint.name
                                is Destination -> endPoint.address
                                else -> "未知地点"
                            })
                        }

                        routeViewModel.dispatch(
                            RouteViewAction.CalculateRideRoute(
                                startGeoCoordinates,
                                endGeoCoordinates
                            )
                        )
                    } else {
                        Log.e(TAG, "fetchRouteData: 起点信息获取失败")
                        ToastUtils.show("未获取到起点信息")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "fetchRouteData: 获取路线数据异常", e)
                    ToastUtils.show("获取路线数据失败: ${e.message}")
                }
            }
        }
    }


    /**
     * 更新起点信息并重新计算路线
     */
    private fun updateStartPoint(selectedAddress: SearchAddress) {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                // 保存自定义起点
                customStartPoint = selectedAddress
                
                // 更新起点显示
                binding.mapRoute.setStart(selectedAddress.name)

                // 获取当前使用的终点信息
                val endPoint = getCurrentEndPoint()

                // 重新计算路线
                if (endPoint != null) {
                    val startGeoCoordinates = GeoCoordinates(
                        selectedAddress.point_latitude.toDouble(),
                        selectedAddress.point_longitude.toDouble()
                    )

                    val endGeoCoordinates = createGeoCoordinatesFromEndPoint(endPoint)

                    startCoordinates = startGeoCoordinates
                    endCoordinates = endGeoCoordinates

                    routeViewModel.dispatch(
                        RouteViewAction.CalculateRideRoute(startGeoCoordinates, endGeoCoordinates)
                    )

                    Log.d(TAG, "重新计算路线: 起点=${selectedAddress.name}")
                } else {
                    ToastUtils.show("未获取到终点信息")
                }
            } catch (e: Exception) {
                Log.e(TAG, "更新起点失败: ${e.message}")
                ToastUtils.show("更新起点失败")
            }
        }
    }

    /**
     * 更新终点信息并重新计算路线
     */
    private fun updateEndPoint(selectedAddress: SearchAddress) {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                // 保存自定义终点
                customEndPoint = selectedAddress
                
                // 保存新的目的地信息
                val newDestination = Destination(
                    latitude = selectedAddress.point_latitude.toDouble(),
                    longitude = selectedAddress.point_longitude.toDouble(),
                    address = selectedAddress.name,
                    district = selectedAddress.district
                )
                context?.saveDestination(newDestination)

                // 更新终点显示
                binding.mapRoute.setDestination(selectedAddress.name)

                // 获取当前使用的起点信息
                val startGeoCoordinates = getCurrentStartGeoCoordinates()
                val endGeoCoordinates = GeoCoordinates(newDestination.latitude, newDestination.longitude)

                if (startGeoCoordinates != null) {
                    startCoordinates = startGeoCoordinates
                    endCoordinates = endGeoCoordinates

                    // 更新起点显示
                    val startName = getStartDisplayName()
                    _binding?.mapRoute?.setStart(startName)

                    routeViewModel.dispatch(
                        RouteViewAction.CalculateRideRoute(
                            startGeoCoordinates,
                            endGeoCoordinates
                        )
                    )
                } else {
                    Log.e(TAG, "获取起点信息失败")
                    ToastUtils.show("未获取到起点信息")
                }

                Log.d(TAG, "重新计算路线: 终点=${selectedAddress.name}")
            } catch (e: Exception) {
                Log.e(TAG, "更新终点失败: ${e.message}")
                ToastUtils.show("更新终点失败")
            }
        }
    }

    private fun startNavi() {
        val context = requireContext()
        if (XXPermissions.isGranted(context, Permission.ACCESS_BACKGROUND_LOCATION)) {
            dispatchStartNavigation()
        } else {
            requestBackgroundLocationPermission(context)
        }
    }

    private fun dispatchStartNavigation() {
        routeViewModel.dispatch(RouteViewAction.StartNavi)
    }

    private fun requestBackgroundLocationPermission(context: Context) {
        XXPermissions.with(context)
            .permission(locationPermissions)
            .permission(Permission.ACCESS_BACKGROUND_LOCATION)
            .request(object : OnPermissionCallback {
                override fun onDenied(
                    grantedPermissions: MutableList<String>,
                    isDoNotAskAgainChecked: Boolean
                ) {
                    XXPermissions.startPermissionActivity(context, grantedPermissions)
                }

                override fun onGranted(
                    grantedPermissions: MutableList<String>,
                    areAllPermissionsGranted: Boolean
                ) {
                    if (areAllPermissionsGranted) dispatchStartNavigation()
                }
            })
    }

    /**
     * 获取当前使用的终点
     * 优先使用用户自定义的终点，否则使用系统保存的目的地
     */
    private fun getCurrentEndPoint(): Any? {
        return customEndPoint ?: context?.getDestination()
    }
    
    /**
     * 从终点对象创建GeoCoordinates（支持SearchAddress和Destination）
     */
    private fun createGeoCoordinatesFromEndPoint(endPoint: Any): GeoCoordinates {
        return when (endPoint) {
            is SearchAddress -> GeoCoordinates(
                endPoint.point_latitude.toDouble(),
                endPoint.point_longitude.toDouble()
            )
            is Destination -> GeoCoordinates(endPoint.latitude, endPoint.longitude)
            else -> throw IllegalArgumentException("Unsupported end point type: ${endPoint::class.java}")
        }
    }
    
    /**
     * 获取当前使用的起点坐标
     * 优先使用用户自定义的起点，否则使用当前位置
     */
    private suspend fun getCurrentStartGeoCoordinates(): GeoCoordinates? {
        // 如果用户设置了自定义起点，优先使用
        customStartPoint?.let { customStart ->
            return GeoCoordinates(
                customStart.point_latitude.toDouble(),
                customStart.point_longitude.toDouble()
            )
        }
        
        // 否则使用当前位置作为起点
        val currentDeviceLocation = RiderLink.instance.getLbsLocation()
        val startLocation = currentDeviceLocation?.run {
            RiderLink.instance.geocodeSearch(requireContext(), coordinates)
        }
        return startLocation?.geoCoordinates
    }
    
    /**
     * 获取起点显示名称
     */
    private suspend fun getStartDisplayName(): String {
        // 如果用户设置了自定义起点，返回其名称
        customStartPoint?.let { customStart ->
            return customStart.name
        }
        
        // 否则返回当前位置名称
        val currentDeviceLocation = RiderLink.instance.getLbsLocation()
        val startLocation = currentDeviceLocation?.run {
            RiderLink.instance.geocodeSearch(requireContext(), coordinates)
        }
        return startLocation?.title ?: "当前位置"
    }

    private fun initTheme() = binding.mapRoute.initTheme(
        { ThemeManager.getCurrentThemeRes(requireContext(), it) },
        { dayThemeString, nightThemeString ->
            ThemeManager.autoChangeStr(
                dayThemeString,
                nightThemeString
            )
        },
        { ThemeManager.isNightMode(requireContext()) }
    )

    override fun onDestroyView() {
        super.onDestroyView()
        changeSystemBarColorActually(Color.TRANSPARENT, Color.TRANSPARENT)
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        
        // 清除自定义的起点和终点
        customStartPoint = null
        customEndPoint = null
        
        _binding = null
    }

    companion object {
        private const val TAG = "RouteFragment"
    }
}