package com.link.riderlink.features.home

import android.R.attr.fragment
import android.annotation.SuppressLint
import android.app.Dialog
import android.graphics.Color
import android.os.Build
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.View
import android.view.ViewGroup.LayoutParams
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.link.riderlink.R
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.WifiStatus
import com.link.riderservice.feature.connection.ble.BleDevice

/**
 * HomeFragment相关Dialog工具类
 * 所有方法均为静态，便于Fragment中直接调用
 */
object HomeDialogs {
    /**
     * 注册Dialog的主题切换监听，自动解绑
     */
    fun registerDialogThemeListener(
        fragment: Fragment,
        dialog: Dialog,
        backgroundView: View,
        backgroundResourceID: Int
    ): ThemeManager.OnThemeChangeListener {
        val listener = object : ThemeManager.OnThemeChangeListener() {
            override fun onThemeChanged() {
                backgroundView.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        fragment.requireContext(),
                        backgroundResourceID
                    )
                )
            }
        }
        ThemeManager.registerThemeChangeListener(listener)
        dialog.setOnDismissListener { ThemeManager.unregisterThemeChangeListener(listener) }
        return listener
    }

    /**
     * 显示连接状态Dialog
     */
    fun showConnectDialog(
        fragment: Fragment,
        isConnect: Boolean,
        status: Connection,
        isOtherDialogShowing: Boolean = false,
        onDisconnect: () -> Unit
    ): Dialog? {
        if (!fragment.isVisible || isOtherDialogShowing) return null
        if (status.btStatus is BleStatus.DeviceConnected && status.wifiStatus is WifiStatus.DeviceConnected && isConnect) {
            return null
        }
        val dialog = Dialog(fragment.requireContext(), R.style.POLICY_DIALOG)
        dialog.setContentView(R.layout.connect_dialog)
        dialog.window?.apply {
            setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
            setGravity(Gravity.BOTTOM)
            val layoutParams = attributes
            layoutParams.width = LayoutParams.MATCH_PARENT
            layoutParams.y = 60
            attributes = layoutParams
        }
        dialog.show()
        val titleTextView = dialog.findViewById<TextView>(R.id.connect_dialog_title)
        val contentTextView = dialog.findViewById<TextView>(R.id.connect_dialog_content)
        val cancelButton = dialog.findViewById<ConstraintLayout>(R.id.connect_dialog_button)
        val cancel2Button = dialog.findViewById<ConstraintLayout>(R.id.connect_dialog_button_cancel)
        val okButton = dialog.findViewById<ConstraintLayout>(R.id.connect_dialog_button_ok)
        val backgroundView = dialog.findViewById<View>(R.id.connect_dialog_background_img)
        backgroundView?.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                fragment.requireContext(),
                R.drawable.connect_dialog_background
            )
        )
        cancelButton?.setOnClickListener { dialog.dismiss() }
        cancel2Button?.setOnClickListener { dialog.dismiss() }
        okButton?.setOnClickListener {
            onDisconnect()
            dialog.dismiss()
        }
        if (isConnect) {
            titleTextView?.text = fragment.getString(R.string.connected_dialog_title)
            contentTextView?.text = fragment.getString(R.string.connected_dialog_content)
            cancelButton?.visibility = View.VISIBLE
            cancel2Button?.visibility = View.GONE
            okButton?.visibility = View.GONE
        } else {
            titleTextView?.text = fragment.getString(R.string.disconnected_dialog_title)
            contentTextView?.text = fragment.getString(R.string.disconnected_dialog_content)
            cancelButton?.visibility = View.GONE
            cancel2Button?.visibility = View.VISIBLE
            okButton?.visibility = View.VISIBLE
        }
        // 主题切换监听
        backgroundView?.let {
            registerDialogThemeListener(fragment, dialog, it, R.drawable.connect_dialog_background)
        }
        return dialog
    }

    /**
     * 显示设备列表Dialog
     */
    fun showConnectListDialog(
        fragment: Fragment,
        adapter: DeviceAdapter,
        deviceListProvider: (LifecycleOwner, (List<BleDevice>) -> Unit) -> Unit,
        isWifiOpenProvider: (LifecycleOwner, (Boolean) -> Unit) -> Unit,
        onStartScan: () -> Unit,
        isConnected: () -> Boolean = { false },
        onConnectStatusChange: ((adapter: DeviceAdapter) -> Unit) = {},
        onStatusChange: ((View?, View?, View?) -> Unit) = { _, _, _ -> }
    ): Dialog {
        val dialog = BottomSheetDialog(fragment.requireContext(), R.style.ConnectDialog)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.connect_list_dialog)
        val window = dialog.window!!
        window.setWindowAnimations(R.style.BottomDialog_Animation)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            window.isNavigationBarContrastEnforced = false
        }
        window.navigationBarColor = Color.TRANSPARENT
        window.statusBarColor = Color.TRANSPARENT
        ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { v, insets ->
            v.setPadding(0, insets.systemWindowInsetTop, 0, 0)
            insets.consumeSystemWindowInsets()
        }
        dialog.show()
        val startButton = dialog.findViewById<ConstraintLayout>(R.id.connect_list_dialog_button)
        val startIconImageView = dialog.findViewById<ImageView>(R.id.img_dialog_button)
        val startButtonTextView = dialog.findViewById<TextView>(R.id.tv_dialog_button)
        val titleTextView = dialog.findViewById<TextView>(R.id.connect_list_dialog_title)
        val contentTextView = dialog.findViewById<TextView>(R.id.connect_list_dialog_content)
        val rescanButton = dialog.findViewById<ConstraintLayout>(R.id.connect_list_dialog_button_re)
        val carIconImageView = dialog.findViewById<ImageView>(R.id.connect_list_dialog_bitmap)
        val backButton1 = dialog.findViewById<ImageView>(R.id.connect_list_dialog_cancel)
        val backButton2 = dialog.findViewById<ConstraintLayout>(R.id.connect_list_dialog_button_cancel)
        val deviceList = dialog.findViewById<RecyclerView>(R.id.connect_dialog_list)
        val backgroundView = dialog.findViewById<ImageView>(R.id.connect_dialog_list_background_img)
        
        // 设置文本颜色
        contentTextView?.setTextColor(
            fragment.resources.getColor(
                ThemeManager.autoChangeInt(
                    R.color.grey_tx,
                    R.color.blue_tx
                ), null
            )
        )
        titleTextView?.setTextColor(
            fragment.resources.getColor(
                ThemeManager.autoChangeInt(
                    R.color.black_tx,
                    R.color.blue_tx
                ), null
            )
        )
        
        // 创建主题切换监听器
        val callback = object : ThemeManager.OnThemeChangeListener() {
            @SuppressLint("NotifyDataSetChanged")
            override fun onThemeChanged() {
                contentTextView?.setTextColor(
                    fragment.resources.getColor(
                        ThemeManager.autoChangeInt(
                            R.color.grey_tx,
                            R.color.blue_tx
                        ), null
                    )
                )
                titleTextView?.setTextColor(
                    fragment.resources.getColor(
                        ThemeManager.autoChangeInt(
                            R.color.black_tx,
                            R.color.blue_tx
                        ), null
                    )
                )
                backgroundView?.setImageResource(
                    ThemeManager.getCurrentThemeRes(
                        fragment.requireContext(),
                        R.drawable.connect_list_background
                    )
                )
                deviceList?.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        fragment.requireContext(),
                        R.drawable.connect_dialog_list_background
                    )
                )
                adapter.notifyDataSetChanged()
            }
        }
        
        // 设置背景资源
        backgroundView?.setImageResource(
            ThemeManager.getCurrentThemeRes(
                fragment.requireContext(),
                R.drawable.connect_list_background
            )
        )
        deviceList?.setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                fragment.requireContext(),
                R.drawable.connect_dialog_list_background
            )
        )
        
        // 设置RecyclerView
        deviceList?.layoutManager = LinearLayoutManager(fragment.activity)
        deviceList?.adapter = adapter
        
        // 获取设备列表和WiFi状态
        deviceListProvider(fragment.viewLifecycleOwner, adapter::submitList)
        isWifiOpenProvider(fragment.viewLifecycleOwner) {
            if (it) carIconImageView?.setImageResource(R.drawable.wifi_ble) else carIconImageView?.setImageResource(R.drawable.ble_only)
        }
        
        // 连接状态变化处理
        onConnectStatusChange(adapter)
        onStatusChange(startButton, rescanButton, backButton2)
        
        // 如果已经连接，更新UI状态
        if (isConnected()) {
            startIconImageView?.alpha = 0.5F
            startButtonTextView?.text = fragment.getString(R.string.search_text)
            startButton?.isClickable = false
            carIconImageView?.visibility = View.GONE
            deviceList?.visibility = View.VISIBLE
            contentTextView?.visibility = View.GONE
        }
        
        // 设置点击事件
        startButton?.setOnClickListener {
            onStartScan()
            startIconImageView?.alpha = 0.5F
            startButtonTextView?.text = fragment.getString(R.string.search_text)
            startButton.isClickable = false
            carIconImageView?.visibility = View.GONE
            deviceList?.visibility = View.VISIBLE
            contentTextView?.visibility = View.GONE
        }
        
        rescanButton?.setOnClickListener { onStartScan() }
        backButton1?.setOnClickListener { dialog.dismiss() }
        backButton2?.setOnClickListener { 
            ThemeManager.unregisterThemeChangeListener(callback)
            dialog.dismiss() 
        }
        
        // 注册主题切换监听器
        ThemeManager.registerThemeChangeListener(callback)
        
        return dialog
    }

    /**
     * 显示隐私政策Dialog
     */
    fun showRuleDialog(
        fragment: Fragment,
        title: String?,
        tagColor: Int,
        onAgree: () -> Unit,
        onRefuse: () -> Unit
    ): Dialog {
        val policyText = fragment.getString(R.string.policy_text)
        val dialog = Dialog(fragment.requireContext(), R.style.POLICY_DIALOG)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.privacy_first_dialog)
        dialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        dialog.show()
        val window = dialog.window
        window?.attributes =
            window?.attributes?.apply { width = android.app.ActionBar.LayoutParams.MATCH_PARENT }
        val tvOk = dialog.findViewById<ConstraintLayout>(R.id.privacy_dialog_button_ok)
        val tvCancel = dialog.findViewById<ConstraintLayout>(R.id.privacy_dialog_button_cancel)
        val titleTextView = dialog.findViewById<TextView>(R.id.privacy_dialog_title)
        val tvText = dialog.findViewById<TextView>(R.id.privacy_dialog_content)
        val backgroundView = dialog.findViewById<View>(R.id.privacy_dialog_background_img)
        val dialogBackgroundTv = dialog.findViewById<View>(R.id.privacy_dialog_text_background)
        backgroundView?.let {
            registerDialogThemeListener(fragment, dialog, it, R.drawable.privacy_dialog_background)
        }
        dialogBackgroundTv?.let {
            registerDialogThemeListener(
                fragment,
                dialog,
                it,
                R.drawable.privacy_dialog_text_background
            )
        }
        titleTextView?.setTextColor(
            fragment.resources.getColor(
                ThemeManager.autoChangeInt(R.color.black_tx, R.color.blue_tx), null
            )
        )
        tvText?.setTextColor(
            fragment.resources.getColor(
                ThemeManager.autoChangeInt(R.color.grey_tx, R.color.white_tx), null
            )
        )
        titleTextView?.text = title
        val tag1 = "《"
        val tag2 = "》"
        val firstIndex = policyText.indexOf(tag1)
        val secondIndex = policyText.indexOf(tag2) + 1
        val style = SpannableStringBuilder().apply { append(policyText) }
        val clickableSpanOne: ClickableSpan = object : ClickableSpan() {
            override fun onClick(v: View) {
                dialog.dismiss()
                onRefuse()
            }
        }
        style.setSpan(clickableSpanOne, firstIndex, secondIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        val foregroundColorSpanOne =
            ForegroundColorSpan(fragment.resources.getColor(tagColor, null))
        style.setSpan(
            foregroundColorSpanOne,
            firstIndex,
            secondIndex,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tvText?.movementMethod = LinkMovementMethod.getInstance()
        tvText?.text = style
        tvOk?.setOnClickListener {
            onAgree()
            dialog.dismiss()
        }
        tvCancel?.setOnClickListener {
            dialog.dismiss()
            onRefuse()
        }
        return dialog
    }

    /**
     * 显示拒绝隐私政策Dialog
     */
    fun showRuleRefusedDialog(
        fragment: Fragment,
        onAgree: () -> Unit,
        onExit: () -> Unit
    ): Dialog {
        val policyText = fragment.getString(R.string.policy_refused_text)
        val dialog = Dialog(fragment.requireContext(), R.style.POLICY_DIALOG)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.privacy_refused_dialog)
        dialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        dialog.show()
        val tvOk = dialog.findViewById<ConstraintLayout>(R.id.privacy_dialog_button_refused_ok)
        val tvCancel =
            dialog.findViewById<ConstraintLayout>(R.id.privacy_dialog_button_refused_exit)
        val titleTextView = dialog.findViewById<TextView>(R.id.privacy_dialog_refused_title)
        val tvText = dialog.findViewById<TextView>(R.id.privacy_dialog_refused_content)
        val backgroundView = dialog.findViewById<View>(R.id.privacy_dialog_refused_background)
        backgroundView?.let {
            registerDialogThemeListener(
                fragment,
                dialog,
                it,
                R.drawable.privacy_dialog_refused_background
            )
        }
        titleTextView?.setTextColor(
            fragment.resources.getColor(
                ThemeManager.autoChangeInt(R.color.black_tx, R.color.blue_tx), null
            )
        )
        tvText?.setTextColor(
            fragment.resources.getColor(
                ThemeManager.autoChangeInt(R.color.grey_tx, R.color.white_tx), null
            )
        )
        val tag1 = "《"
        val tag2 = "》"
        val firstIndex = policyText.indexOf(tag1)
        val secondIndex = policyText.indexOf(tag2) + 1
        val style = SpannableStringBuilder().apply { append(policyText) }
        val clickableSpanOne: ClickableSpan = object : ClickableSpan() {
            override fun onClick(v: View) {
                dialog.dismiss()
                onAgree()
            }
        }
        style.setSpan(clickableSpanOne, firstIndex, secondIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        val foregroundColorSpanOne =
            ForegroundColorSpan(fragment.resources.getColor(R.color.link, null))
        style.setSpan(
            foregroundColorSpanOne,
            firstIndex,
            secondIndex,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tvText?.movementMethod = LinkMovementMethod.getInstance()
        tvText?.text = style
        tvOk?.setOnClickListener {
            onAgree()
            dialog.dismiss()
        }
        tvCancel?.setOnClickListener {
            onExit()
            dialog.dismiss()
        }
        return dialog
    }
} 