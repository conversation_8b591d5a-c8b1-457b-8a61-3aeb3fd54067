package com.link.riderlink.features.home

import android.R.attr.fragment
import android.annotation.SuppressLint
import android.app.ActionBar.LayoutParams
import android.app.Activity.RESULT_OK
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.activity.addCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isGone
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.ToastUtils
import com.link.riderdvr.utils.mainScope
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.FragmentHomeBinding
import com.link.riderlink.service.RiderLinkProjectionService
import com.link.riderlink.service.events.MediaProjectionEvent
import com.link.riderlink.ui.components.home.ConnectInfoMange
import com.link.riderlink.ui.extensions.IS_AGREE_POLICY
import com.link.riderlink.ui.extensions.IS_BLUETOOTH_DENIED
import com.link.riderlink.ui.extensions.IS_LOCATION_DENIED
import com.link.riderlink.ui.extensions.getBoolean
import com.link.riderlink.ui.extensions.navigate
import com.link.riderlink.ui.extensions.observeEvent
import com.link.riderlink.ui.extensions.observeState
import com.link.riderlink.ui.extensions.putBoolean
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.utils.connectivity.isNetworkAvailable
import com.link.riderlink.utils.system.PermissionsUtil
import com.link.riderlink.utils.system.connectPermissions
import com.link.riderlink.utils.system.locationPermissions
import com.link.riderservice.api.dto.BleStatus
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.WifiStatus
import com.link.riderservice.feature.connection.ble.BleDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class HomeFragment : Fragment() {
    private lateinit var launcher: ActivityResultLauncher<Intent>
    private val homeViewModel: HomeViewModel by viewModels()
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!

    private var privacyDialog: Dialog? = null
    private var connectDialog: Dialog? = null
    private var connectListDialog: Dialog? = null
    private var isHomePage: Boolean = true
    private var mAutolinkServiceIntent: Intent? = null
    private var isSecondary = false

    private val onBleDeviceClick = { menuItem: BleDevice ->
        homeViewModel.dispatch(HomeViewAction.BleItemClicked(menuItem))
    }
    private val onBleDeviceLongClick = { view: View, item: BleDevice ->
        showDevicePopupMenu(view, item)
    }
    private val adapter: DeviceAdapter by lazy {
        DeviceAdapter(requireContext(), onBleDeviceClick, onBleDeviceLongClick)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMediaProjectionEvent(event: MediaProjectionEvent?) {
        Log.d(TAG, "onMediaProjectionEvent: ")
        if (event != null && event.intent != null) {
            val mediaProjectionManager: MediaProjectionManager =
                context?.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            val mediaProjection =
                mediaProjectionManager.getMediaProjection(event.resultCode, event.intent)
            RiderLink.instance.setMediaProjection(mediaProjection)
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        Log.d(TAG, "onAttach:")
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            if (connectDialog?.isShowing == true) {
                connectDialog?.dismiss()
            }
        }
        launcher =
            requireActivity().registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
                Log.d(TAG, "launcher: $it")
                if (it.resultCode == RESULT_OK) {
                    val data: Intent? = it.data
                    mAutolinkServiceIntent =
                        Intent(requireActivity(), RiderLinkProjectionService::class.java)
                    mAutolinkServiceIntent?.putExtra("resultCode", it.resultCode)
                    mAutolinkServiceIntent?.putExtra("data", data)
                    mAutolinkServiceIntent?.putExtra("isSecondary", isSecondary)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        requireActivity().startForegroundService(mAutolinkServiceIntent)
                    }else {
                        requireActivity().startService(mAutolinkServiceIntent)
                    }
                } else {
                    ToastUtils.show(getString(R.string.no_mediaprojection_permission))
                    RiderLink.instance.stopMirror()
                }
            }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observeViewModel()
        setupClickListeners()
        mainScope.launch(Dispatchers.Main) {
            delay(2000L)
            privacyDialog = showRuleDialog(resources.getString(R.string.policy_title), R.color.link)
        }
        ThemeManager.registerThemeChangeListener(themeCallback)
        initTheme()
        isHomePage = true
    }

    // 观察ViewModel的状态和事件
    private fun observeViewModel() {
        homeViewModel.viewEvents.observeEvent(this) { event ->
            when (event) {
                is HomeViewEvent.UpdateLocation -> if (isHomePage) binding.mainLocationTv.text =
                    event.place?.title

                is HomeViewEvent.ChangeIdleText -> if (isHomePage) binding.connectStateText.text =
                    ConnectInfoMange.getIdleText()

                else -> {}
            }
        }
        homeViewModel.viewStates.observeState(
            viewLifecycleOwner,
            HomeViewState::status
        ) {
            updateConnectState(it)
            if (it.btStatus is BleStatus.DeviceConnected) {
                requestLocation()
            }
        }
        homeViewModel.viewStates.observeState(
            viewLifecycleOwner,
            HomeViewState::isMirror
        ) { updateMirrorState(it) }
        homeViewModel.viewStates.observeState(
            viewLifecycleOwner,
            HomeViewState::config
        ) {
            changeView(it.isSupportNavi, it.isSupportDvr)
        }
    }

    // 设置所有点击事件
    private fun setupClickListeners() {
        binding.buttonConnect.setOnClickListener { connectListDialog = showConnectListDialog() }
        binding.buttonDisconnect.setOnClickListener { connectDialog = showConnectDialog(false) }
        binding.buttonMirror.setOnClickListener {
            homeViewModel.dispatch(
                HomeViewAction.Mirror(
                    launcher
                )
            )
        }
        binding.menuMore.setOnClickListener { navigate(R.id.action_homeFragment_to_settingFragment) }
        binding.mainButtonMap.setOnClickListener {
            if (!isNetworkAvailable(context, true)) {
                ToastUtils.show(getString(R.string.no_network_message))
            } else {
                navigate(R.id.action_homeFragment_to_mapFragment)
            }
        }
        binding.mainButtonDvr.setOnClickListener { navigate(R.id.action_homeFragment_to_dvrFragment) }
        binding.mainButtonQqM.setOnClickListener { /* 可扩展QQM点击事件 */ }
    }

    // 更新连接状态UI
    private fun updateConnectState(status: Connection) {
        binding.connectStateText.text = ConnectInfoMange.updateConnectState(context, status)

        // 处理连接中状态
        if (status.btStatus is BleStatus.DeviceConnecting) {
            connectDialog?.dismiss()
        }

        // 使用when表达式处理不同的连接状态
        when {
            // 蓝牙和WiFi都已连接
            (status.btStatus is BleStatus.DeviceConnected) && (status.wifiStatus is WifiStatus.DeviceConnected) -> {
                with(binding) {
                    connectStatePoint.setImageResource(R.drawable.connected_point)
                    connectStateText.setTextColor(
                        ThemeColors.STATUS_SUCCESS.getCurrentColorInt(
                            requireContext()
                        )
                    )
                    buttonConnect.visibility = View.GONE
                    connectStateSign.visibility = View.VISIBLE
                    buttonDisconnect.visibility = View.VISIBLE
                    buttonMirror.visibility = View.VISIBLE
                    mainLocationBitmap.setImageResource(R.drawable.location_connected)
                    mainButtonMap.setBitImage(if (homeViewModel.viewStates.value.config.isSupportDvr) R.drawable.main_tool_bitmap_map1 else R.drawable.main_tool_bitmap_map3)
                    mainButtonMap.isClickAble(false)
                    mainButtonDvr.setBitImage(R.drawable.main_tool_bitmap_dvr2)
                    mainButtonDvr.isClickAble(false)
                }
                connectDialog?.dismiss()
            }
            // 蓝牙和WiFi都已断开
            status.btStatus is BleStatus.DeviceDisconnected && status.wifiStatus is WifiStatus.DeviceDisconnected -> {
                with(binding) {
                    connectStatePoint.setImageResource(
                        ThemeManager.autoChangeInt(
                            R.drawable.un_connect_point,
                            R.drawable.un_connect_point_night
                        )
                    )
                    connectStateText.setTextColor(
                        ThemeColors.STATUS_ERROR.getCurrentColorInt(
                            requireContext()
                        )
                    )
                    connectStateSign.visibility = View.GONE
                    buttonConnect.visibility = View.VISIBLE
                    buttonDisconnect.visibility = View.GONE
                    buttonMirror.visibility = View.GONE
                    mainLocationBitmap.setImageResource(R.drawable.location_unconnect)
                    mainButtonMap.setBitImage(if (homeViewModel.viewStates.value.config.isSupportDvr) R.drawable.main_tool_bitmap_map1 else R.drawable.main_tool_bitmap_map3)
                    mainButtonMap.isClickAble(true)
                    mainButtonDvr.setBitImage(R.drawable.main_tool_bitmap_dvr1)
                    mainButtonDvr.isClickAble(true)
                }
            }
        }
    }

    // 更新镜像按钮UI
    private fun updateMirrorState(isMirror: Boolean) {
        with(binding.buttonMirror) {
            setText(if (isMirror) getString(R.string.stop_mirror) else getString(R.string.mirror))
            setTextColor(
                if (isMirror) ThemeManager.autoChangeInt(
                    R.color.blue_tx,
                    R.color.white_tx
                ) else R.color.black_tx
            )
            setBackgroundImage(
                if (isMirror) ThemeManager.autoChangeInt(
                    R.drawable.button_connect_white_select,
                    R.drawable.button_connect_blue_select
                ) else R.drawable.button_connect_white_select
            )
            setBitmap(
                if (isMirror) ThemeManager.getCurrentThemeRes(
                    requireContext(),
                    R.drawable.mirrored
                ) else R.drawable.mirror
            )
        }
    }

    // 主题切换回调
    private val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    // 根据功能支持情况切换主界面按钮
    private fun changeView(isSupportNavi: Boolean, isSupportDvr: Boolean) {
        with(binding) {
            if (!isSupportNavi && !isSupportDvr) {
                mainButtonMap.visibility = View.GONE
                mainButtonDvr.visibility = View.GONE
                mainButtonQqM.visibility = View.GONE
                return
            }
            if (isSupportNavi && isSupportDvr) {
                mainButtonMap.visibility = View.VISIBLE
                mainButtonDvr.visibility = View.VISIBLE
                mainButtonQqM.visibility = View.VISIBLE
                mainButtonMap.setBitImage(R.drawable.main_tool_bitmap_map1)
                ConstraintSet().apply {
                    constrainWidth(mainLocationBitmap.id, 84)
                    constrainHeight(mainLocationBitmap.id, 84)
                    connect(
                        mainLocationBitmap.id,
                        ConstraintSet.RIGHT,
                        mainButtonMap.id,
                        ConstraintSet.RIGHT,
                        183
                    )
                    connect(
                        mainLocationBitmap.id,
                        ConstraintSet.TOP,
                        mainButtonMap.id,
                        ConstraintSet.TOP,
                        69
                    )
                    constrainWidth(mainButtonMap.id, LayoutParams.MATCH_PARENT)
                    constrainHeight(mainButtonMap.id, 318)
                }.applyTo(mainToolBox)
            } else if (isSupportNavi) {
                mainButtonMap.visibility = View.VISIBLE
                mainButtonDvr.visibility = View.GONE
                mainButtonQqM.visibility = View.GONE
                mainButtonMap.setBitImage(R.drawable.main_tool_bitmap_map3)
                ConstraintSet().apply {
                    constrainWidth(mainLocationBitmap.id, 120)
                    constrainHeight(mainLocationBitmap.id, 120)
                    connect(
                        mainLocationBitmap.id,
                        ConstraintSet.LEFT,
                        mainButtonMap.id,
                        ConstraintSet.LEFT,
                        450
                    )
                    connect(
                        mainLocationBitmap.id,
                        ConstraintSet.TOP,
                        mainButtonMap.id,
                        ConstraintSet.TOP,
                        198
                    )
                    constrainWidth(mainButtonMap.id, LayoutParams.MATCH_PARENT)
                    constrainHeight(mainButtonMap.id, 510)
                }.applyTo(mainToolBox)
            }
        }
    }

    // 权限请求
    private fun requestLocation() {
        XXPermissions.with(requireContext())
            .permission(Permission.ACCESS_FINE_LOCATION)
            .permission(Permission.ACCESS_COARSE_LOCATION)
            .request { _: List<String?>?, allPermissionsGranted: Boolean ->
                if (allPermissionsGranted) {
                    homeViewModel.dispatch(HomeViewAction.StartLocation)
                } else {
                    ToastUtils.show(getString(R.string.no_permission_message))
                }
            }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        privacyDialog?.takeIf { it.isShowing }?.apply { dismiss() }
        isHomePage = false
        _binding = null
        Log.d(TAG, "onDestroyView: ")
    }

    // Dialog主题监听注册与解绑
    private fun registerDialogThemeListener(
        dialog: Dialog,
        backgroundView: View,
        resId: Int
    ): ThemeManager.OnThemeChangeListener {
        val onPermissionResult = object : ThemeManager.OnThemeChangeListener() {
            override fun onThemeChanged() {
                backgroundView.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        resId
                    )
                )
            }
        }
        ThemeManager.registerThemeChangeListener(onPermissionResult)
        dialog.setOnDismissListener { ThemeManager.unregisterThemeChangeListener(onPermissionResult) }
        return onPermissionResult
    }

    // 显示连接状态Dialog
    private fun showConnectDialog(isConnect: Boolean): Dialog? {
        val isOtherDialogShowing = connectListDialog?.isShowing == true
        return HomeDialogs.showConnectDialog(
            this,
            isConnect,
            homeViewModel.viewStates.value.status,
            isOtherDialogShowing
        ) {
            homeViewModel.dispatch(HomeViewAction.Disconnect)
        }?.also { connectDialog = it }
    }

    // 显示设备列表Dialog
    private fun showConnectListDialog(): Dialog? {
        connectListDialog = HomeDialogs.showConnectListDialog(
            fragment = this,
            adapter = adapter,
            deviceListProvider = { lifecycleOwner, submit ->
                homeViewModel.viewStates.observeState(
                    lifecycleOwner,
                    HomeViewState::bleList
                ) {
                    submit(it)
                }
            },
            isWifiOpenProvider = { lifecycleOwner, submit ->
                homeViewModel.viewStates.observeState(lifecycleOwner, HomeViewState::isWifiOpen) {
                    submit(it)
                }
            },
            onStartScan = {
                homeViewModel.dispatch(HomeViewAction.StartScan(false))
            },
            isConnected = {
                homeViewModel.viewStates.value.status.btStatus is BleStatus.DeviceConnected
            },
            onConnectStatusChange = { adapterInstance ->
                homeViewModel.viewEvents.observeEvent(this) { event ->
                    if (event is HomeViewEvent.ConnectStatusChange) {
                        adapterInstance.currentList.forEachIndexed { index, _ ->
                            adapterInstance.notifyItemChanged(index)
                        }
                    }
                }
            },
            onStatusChange = { startBtn, rescanBtn, backBtn ->
                homeViewModel.viewStates.observeState(
                    viewLifecycleOwner,
                    HomeViewState::status
                ) { status ->
                    if ((status.btStatus is BleStatus.DeviceConnected) &&
                        (status.wifiStatus is WifiStatus.DeviceConnected)
                    ) {
                        startBtn?.visibility = View.GONE
                        rescanBtn?.visibility = View.VISIBLE
                        backBtn?.visibility = View.VISIBLE
                    }
                }
            }
        )
        return connectListDialog
    }

    // 显示隐私政策Dialog
    private fun showRuleDialog(title: String?, tagColor: Int): Dialog? {
        if (requireContext().getBoolean(IS_AGREE_POLICY)) {
            start()
            connectDialog = showConnectDialog(true)
            return null
        }
        return HomeDialogs.showRuleDialog(
            this,
            title,
            tagColor,
            onAgree = {
                lifecycleScope.launch { requireContext().putBoolean(IS_AGREE_POLICY, true) }
                start()
            },
            onRefuse = {
                showRuleRefusedDialog()
            }
        )
    }

    // 显示拒绝隐私政策Dialog
    private fun showRuleRefusedDialog(): Dialog {
        return HomeDialogs.showRuleRefusedDialog(
            this,
            onAgree = {
                lifecycleScope.launch { requireContext().putBoolean(IS_AGREE_POLICY, true) }
                start()
            },
            onExit = {
                lifecycleScope.launch { requireContext().putBoolean(IS_AGREE_POLICY, false) }
                requireActivity().finish()
            }
        )
    }

    // 权限和首次启动逻辑
    private fun start() {
        if (XXPermissions.isGranted(requireContext(), locationPermissions)) {
            lifecycleScope.launch { requireContext().putBoolean(IS_LOCATION_DENIED, false) }
        }
        if (XXPermissions.isGranted(requireContext(), connectPermissions)) {
            lifecycleScope.launch { requireContext().putBoolean(IS_BLUETOOTH_DENIED, false) }
        }
        if (XXPermissions.isGranted(requireContext(), connectPermissions, locationPermissions)) {
            homeViewModel.dispatch(HomeViewAction.StartScan())
        } else {
            val onPermissionResult = {
                XXPermissions.with(requireContext())
                    .permission(connectPermissions)
                    .permission(locationPermissions)
                    .request(object : OnPermissionCallback {
                        override fun onGranted(
                            permissions: MutableList<String>,
                            allGranted: Boolean
                        ) {
                            if (allGranted) {
                                homeViewModel.dispatch(HomeViewAction.StartScan())
                                connectDialog = showConnectDialog(true)
                            }
                        }

                        override fun onDenied(
                            permissions: MutableList<String>,
                            doNotAskAgain: Boolean
                        ) {
                            super.onDenied(permissions, doNotAskAgain)
                            if (!XXPermissions.isGranted(requireContext(), locationPermissions)) {
                                lifecycleScope.launch {
                                    requireContext().putBoolean(
                                        IS_LOCATION_DENIED,
                                        true
                                    )
                                }
                            }
                            if (!XXPermissions.isGranted(requireContext(), connectPermissions)) {
                                lifecycleScope.launch {
                                    requireContext().putBoolean(
                                        IS_BLUETOOTH_DENIED,
                                        true
                                    )
                                }
                            }
                        }
                    })
            }
            PermissionsUtil.showRequestPermissionDialog(requireContext(), onPermissionResult)
        }
    }

    // 主题初始化
    private fun initTheme() {
        with(binding) {
            if (connectStateSign.isGone) {
                connectStateText.setTextColor(
                    ThemeColors.STATUS_ERROR.getCurrentColorInt(
                        requireContext()
                    )
                )
            } else {
                connectStateText.setTextColor(
                    ThemeColors.STATUS_SUCCESS.getCurrentColorInt(
                        requireContext()
                    )
                )
            }
            homeViewModel.viewStates.value.isMirror.let {
                buttonMirror.setTextColor(
                    if (it) ThemeManager.autoChangeInt(
                        R.color.blue_tx,
                        R.color.white_tx
                    ) else R.color.black_tx
                )
                buttonMirror.setBackgroundImage(
                    if (it) ThemeManager.autoChangeInt(
                        R.drawable.button_connect_white_select,
                        R.drawable.button_connect_blue_select
                    ) else R.drawable.button_connect_white_select
                )
                buttonMirror.setBitmap(
                    if (it) ThemeManager.getCurrentThemeRes(
                        requireContext(),
                        R.drawable.mirrored
                    ) else R.drawable.mirror
                )
            }
            connectStateSign.setImageResource(
                ThemeManager.getCurrentThemeRes(
                    requireContext(),
                    R.drawable.sign
                )
            )
            menuMore.setImageResource(
                ThemeManager.getCurrentThemeRes(
                    requireContext(),
                    R.drawable.main_menu
                )
            )
            mainButtonMap.onThemeChange(
                ThemeManager.isNightMode(requireContext()),
                requireContext()
            )
            mainButtonDvr.onThemeChange(
                ThemeManager.isNightMode(requireContext()),
                requireContext()
            )
            mainButtonQqM.onThemeChange(
                ThemeManager.isNightMode(requireContext()),
                requireContext()
            )
            homeViewRoot.setBackgroundResource(
                ThemeManager.getCurrentThemeRes(
                    requireContext(),
                    R.drawable.home_page_background
                )
            )
        }
    }

    // 设备长按弹出菜单
    private fun showDevicePopupMenu(anchorView: View, bleDevice: BleDevice) {
        val popupMenu = PopupMenu(requireActivity(), anchorView)
        popupMenu.menuInflater.inflate(R.menu.device_menu, popupMenu.menu)
        popupMenu.setOnMenuItemClickListener {
            if (it.itemId == R.id.disconnect) {
                if (bleDevice.device.address == RiderLink.instance.getCurrentConnectDevice()?.device?.address) {
                    homeViewModel.dispatch(HomeViewAction.SingleDisconnect)
                }
            }
            true
        }
        popupMenu.show()
    }

    companion object {
        private const val TAG = "HomeFragment"
    }
}