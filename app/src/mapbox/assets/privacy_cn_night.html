<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RiderLink Privacy Statement</title>
    <style>
        body {
            font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
            background: #2A3042;
            color: #fff;
            margin: 0;
            padding: 0;
            font-size: 15px;
            line-height: 1.6;
        }
        .container {
            max-width: 100%;
            width: 94%;
            margin: 0 auto;
            padding: 16px 0 24px 0;
        }
        h3 {
            text-align: center;
            margin: 16px 0 24px 0;
            font-weight: bold;
            font-size: 1.3em;
            color: #fff;
        }
        h4 {
            text-align: left;
            margin: 20px 0 12px 0;
            font-weight: bold;
            font-size: 1.1em;
            color: #fff;
        }
        .date-info {
            text-align: right;
            font-size: 14px;
            color: #ccc;
            margin: 8px 0 16px 0;
        }
        p {
            text-align: left;
            text-indent: 0;
            margin: 12px 0;
            color: #fff;
        }
        ul.title4 {
            padding-left: 0;
            margin: 12px 0;
        }
        ul.title4 li {
            list-style: none;
            text-align: left;
            text-indent: 0;
            margin: 8px 0;
            color: #fff;
        }
        a {
            color: #40a9ff;
            text-decoration: none;
            word-break: break-all;
        }
        a:hover, a:active {
            color: #096dd9;
            text-decoration: underline;
        }
        .contact {
            text-align: left;
            text-indent: 0;
            margin: 12px 0;
            color: #fff;
        }
        .section-title {
            font-weight: bold;
            font-size: 1.05em;
            margin: 16px 0 8px 0;
            color: #fff;
        }
    </style>
</head>

<body>
<div class="container">
    <h3>RiderLink Privacy Statement</h3>
    
    <div class="article">
        <ul class="title4">
            <li>1. Scope</li>
            <li>2. Collected Information Types</li>
            <li>3. Uses</li>
            <li>4. Your Choices</li>
        </ul>
    </div>
    
    <div class="article">
        <h4>1. Scope</h4>
        <p>
            The Sunplus Online Privacy Statement applies to Sunplus' Web sites and Productions that link to the Statement. Below are the highlights of the Statement.
        </p>
    </div>
    
    <div class="article">
        <h4>2. Collected Information Types</h4>
        <p>
            This software uses MapBox SDK, please read the privacy policy (<a href="https://www.mapbox.com/legal/privacy#product-privacy-policy" target="_blank">https://www.mapbox.com/legal/privacy#product-privacy-policy</a>).
        </p>
        <p>
            Obtain the device serial number to ensure the security of software services;
        </p>
        <p>
            Obtain the external storage permission of the mobile phone to store application configuration information and software running logs;<br>
            Obtain the MAC address of the mobile phone to realize the automatic reconnection function;<br>
            Obtain sensor information to use the APP to display altitude;
        </p>
        <p>
            Apart from this, we do not currently collect any other personal information related to you.
        </p>
    </div>
    
    <div class="article">
        <h4>3. Uses</h4>
        <p>
            To fulfill your requests by us or by others involved in fulfillment.
        </p>
        <p>
            To support products or services you have obtained from us.
        </p>
    </div>
    
    <div class="article">
        <h4>4. Your Choices</h4>
        <p>
            During installing the APK and asking for authorization information, you can selectively refuse our authorization to access relevant information.
        </p>
    </div>
</div>
</body>

</html>