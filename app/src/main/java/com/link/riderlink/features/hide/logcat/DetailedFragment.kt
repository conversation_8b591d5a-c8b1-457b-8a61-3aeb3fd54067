package com.link.riderlink.features.hide.logcat

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.annotation.RequiresApi
import androidx.fragment.app.Fragment
import com.link.riderlink.databinding.FragmentDetailedBinding
import com.link.riderlink.utils.system.ShareFileUtils
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.ui.theme.setThemeTextColor
import com.link.riderlink.ui.theme.setupBackButton
import java.nio.file.Files
import java.nio.file.Paths

class DetailedFragment : Fragment() {
    private var _binding: FragmentDetailedBinding? = null
    private val binding get() = _binding!!
    private val TAG = "DetailedFragment"
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDetailedBinding.inflate(inflater, container, false)
        return binding.root
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        val bundle = arguments
        var path = ""
        bundle?.let {
            val filepath = it.getString("path")
            if (filepath != null) {
                path = filepath
            }
            val title = it.getString("title")
            binding.tvTitle.text = title
            Log.e(TAG, "onViewCreated: $filepath")
            if (filepath != null) {
                getLogcat(filepath)
            }
        }
        binding.txShare.setOnClickListener {
            Log.e(TAG, "onViewCreated: $path")
            ShareFileUtils.shareFile(requireContext(), path)
        }
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    fun initTheme() {
        setupBackButton(binding.ibBack)
        binding.detailRoot.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        setThemeTextColor(
            ThemeColors.PRIMARY_TEXT,
            binding.tvTitle,
            binding.txDetailed
        )
    }

    val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun getLogcat(filepath: String) {
        val path = Paths.get(filepath)
        var tx = ""
        Files.lines(path, Charsets.UTF_8).forEach {
            tx += it + "\n"
//            Log.e(TAG, "getLogcat: $it", )
        }
        Log.e(TAG, "getLogcat: $tx")
        binding.txDetailed.text = tx
        binding.txDetailed.movementMethod = ScrollingMovementMethod.getInstance();
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }
}