package com.link.riderlink.features.hide

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.FragmentRiderlinkversionBinding
import com.link.riderlink.ui.theme.ThemeHelper
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.setupBackButton
import com.link.riderservice.api.callback.RiderServiceCallback
import kotlinx.coroutines.launch

/**
 * RiderLink版本信息显示Fragment
 * 功能：显示RiderLink版本信息，支持复制到剪贴板
 */
class RiderLinkVersionFragment : Fragment() {

    companion object {
        private const val TAG = "RiderLinkVersionFragment"
        private const val PARAM_KEY = "param"

        // 消息常量
        private const val ERROR_VERSION_UNAVAILABLE = "版本信息暂时不可用"
        private const val SUCCESS_COPIED_TO_CLIPBOARD = "已复制到剪贴板"
        private const val ERROR_COPY_FAILED = "复制失败"
    }

    private var _binding: FragmentRiderlinkversionBinding? = null
    private val binding get() = _binding!!

    // 版本信息缓存
    private var versionInfo: String? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        setupBackPressHandler()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRiderlinkversionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initializeFragment()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cleanup()
    }

    // ==================== 初始化方法 ====================

    /**
     * 设置返回按键处理
     */
    private fun setupBackPressHandler() {
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    /**
     * 初始化Fragment
     */
    private fun initializeFragment() {
        setupClickListeners()
        setupServiceCallback()
        parseArguments()
        initTheme()
        ThemeManager.registerThemeChangeListener(themeChangeListener)
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        binding.apply {
            llBack.setOnClickListener { popBackStack() }

            // 长按复制版本信息
            txRiderlinkversion.setOnLongClickListener {
                copyVersionToClipboard()
                true
            }
        }
    }

    /**
     * 设置服务回调
     */
    private fun setupServiceCallback() {
        RiderLink.instance.addConnectCallback(serviceCallback)
    }

    /**
     * 解析传入参数
     */
    private fun parseArguments() {
        arguments?.getString(PARAM_KEY)?.let { param ->
            versionInfo = param
            updateVersionDisplay()
        }
    }

    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            RiderLink.instance.removeConnectCallback(serviceCallback)
        } catch (e: Exception) {
            Log.w(TAG, "Failed to remove connect callback", e)
        }
        ThemeManager.unregisterThemeChangeListener(themeChangeListener)
        _binding = null
    }

    // ==================== 数据处理方法 ====================

    /**
     * 更新版本信息显示
     */
    private fun updateVersionDisplay() {
        binding.txRiderlinkversion.text = versionInfo ?: ERROR_VERSION_UNAVAILABLE
    }

    /**
     * 处理版本信息更新
     */
    private fun handleVersionUpdate(version: String?) {
        versionInfo = if (version.isNullOrBlank()) {
            Log.w(TAG, "Received empty version info")
            ERROR_VERSION_UNAVAILABLE
        } else {
            Log.d(TAG, "Version info updated: $version")
            version
        }
        updateVersionDisplay()
    }

    // ==================== 功能方法 ====================

    /**
     * 复制版本信息到剪贴板
     */
    private fun copyVersionToClipboard() {
        val versionToCopy = versionInfo
        if (versionToCopy.isNullOrBlank() || versionToCopy == ERROR_VERSION_UNAVAILABLE) {
            showToast(ERROR_VERSION_UNAVAILABLE)
            return
        }

        try {
            val clipboardManager =
                requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
            if (clipboardManager != null) {
                val clipData = ClipData.newPlainText("RiderLink Version", versionToCopy)
                clipboardManager.setPrimaryClip(clipData)
                showToast(SUCCESS_COPIED_TO_CLIPBOARD)
                Log.d(TAG, "Version info copied to clipboard")
            } else {
                Log.e(TAG, "ClipboardManager is null")
                showToast(ERROR_COPY_FAILED)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to copy to clipboard", e)
            showToast(ERROR_COPY_FAILED)
        }
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    // ==================== 主题管理 ====================

    /**
     * 初始化主题
     */
    private fun initTheme() {
        setupBackButton(binding.ibBack)
        ThemeHelper.setVersionPageTheme(
            binding.riderlinkRoot,
            binding.tvTitle,
            binding.txRiderlinkversion
        )
    }

    /**
     * 主题变化监听器
     */
    private val themeChangeListener = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    /**
     * 服务回调
     */
    private val serviceCallback = object : RiderServiceCallback() {
        override fun naviVersionResponse(version: String) {
            super.naviVersionResponse(version)
            lifecycleScope.launch {
                handleVersionUpdate(version)
            }
        }
    }
}