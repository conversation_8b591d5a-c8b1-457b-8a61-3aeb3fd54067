package com.link.riderlink.features.hide

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.link.riderlink.databinding.FragmentUuidBinding
import com.link.riderlink.ui.theme.ThemeHelper
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.setupBackButton
import com.link.riderservice.api.RiderService
import kotlinx.coroutines.launch

/**
 * UUID显示Fragment
 * 功能：显示设备UUID，支持复制到剪贴板
 */
class UuidFragment : Fragment() {

    companion object {
        private const val TAG = "UuidFragment"

        // 消息常量
        private const val ERROR_UUID_UNAVAILABLE = "UUID暂时不可用"
        private const val SUCCESS_COPIED_TO_CLIPBOARD = "已复制到剪贴板"
        private const val ERROR_COPY_FAILED = "复制失败"
    }

    private var _binding: FragmentUuidBinding? = null
    private val binding get() = _binding!!

    // UUID缓存
    private var deviceUuid: String? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        setupBackPressHandler()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentUuidBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initializeFragment()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cleanup()
    }

    // ==================== 初始化方法 ====================

    /**
     * 设置返回按键处理
     */
    private fun setupBackPressHandler() {
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    /**
     * 初始化Fragment
     */
    private fun initializeFragment() {
        setupClickListeners()
        loadUuid()
        initTheme()
        ThemeManager.registerThemeChangeListener(themeChangeListener)
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        binding.apply {
            llBack.setOnClickListener { popBackStack() }

            // 长按复制UUID
            txUuid.setOnLongClickListener {
                copyUuidToClipboard()
                true
            }
        }
    }

    /**
     * 清理资源
     */
    private fun cleanup() {
        ThemeManager.unregisterThemeChangeListener(themeChangeListener)
        _binding = null
    }

    // ==================== 数据加载方法 ====================

    /**
     * 加载UUID
     */
    private fun loadUuid() {
        lifecycleScope.launch {
            try {
                deviceUuid = getUuidFromService()
                updateUuidDisplay()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load UUID", e)
                handleUuidError()
            }
        }
    }

    /**
     * 从服务获取UUID
     */
    private fun getUuidFromService(): String? {
        return try {
            val uuid = RiderService.instance.getUuid()
            if (uuid.isBlank()) {
                Log.w(TAG, "UUID is null or empty")
                null
            } else {
                Log.d(TAG, "UUID loaded successfully")
                uuid
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting UUID from service", e)
            null
        }
    }

    /**
     * 更新UUID显示
     */
    private fun updateUuidDisplay() {
        binding.txUuid.text = deviceUuid ?: ERROR_UUID_UNAVAILABLE
    }

    /**
     * 处理UUID加载错误
     */
    private fun handleUuidError() {
        binding.txUuid.text = ERROR_UUID_UNAVAILABLE
        deviceUuid = null
    }

    // ==================== 功能方法 ====================

    /**
     * 复制UUID到剪贴板
     */
    private fun copyUuidToClipboard() {
        val uuidToCopy = deviceUuid
        if (uuidToCopy.isNullOrBlank()) {
            showToast(ERROR_UUID_UNAVAILABLE)
            return
        }

        try {
            val clipboardManager =
                requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
            if (clipboardManager != null) {
                val clipData = ClipData.newPlainText("Device UUID", uuidToCopy)
                clipboardManager.setPrimaryClip(clipData)
                showToast(SUCCESS_COPIED_TO_CLIPBOARD)
                Log.d(TAG, "UUID copied to clipboard")
            } else {
                Log.e(TAG, "ClipboardManager is null")
                showToast(ERROR_COPY_FAILED)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to copy to clipboard", e)
            showToast(ERROR_COPY_FAILED)
        }
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    // ==================== 主题管理 ====================

    /**
     * 初始化主题
     */
    private fun initTheme() {
        setupBackButton(binding.ibBack)
        ThemeHelper.setVersionPageTheme(
            binding.uuidRoot,
            binding.tvTitle,
            binding.txUuid
        )
    }

    /**
     * 主题变化监听器
     */
    private val themeChangeListener = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }
}