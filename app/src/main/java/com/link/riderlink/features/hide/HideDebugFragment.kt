package com.link.riderlink.features.hide

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.core.content.edit
import androidx.fragment.app.Fragment
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.FragmentHidedebugBinding
import com.link.riderlink.utils.system.LogcatHelper
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.ThemeIcons
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.extensions.navigate
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.ui.theme.setThemeTextColor
import com.link.riderlink.ui.theme.setupBackButton
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.callback.RiderServiceCallback
import com.link.riderservice.api.dto.NaviVersionRequest


class HideDebugFragment : Fragment() {
    private var _binding: FragmentHidedebugBinding? = null
    private val binding get() = _binding!!

    private var preferences: SharedPreferences? = null
    private var riderLinkVersion: String = DEFAULT_VERSION_TEXT

    private val serviceCallback = object : RiderServiceCallback() {
        override fun naviVersionResponse(version: String) {
            super.naviVersionResponse(version)
            riderLinkVersion = version
            Log.d(TAG, "naviVersionResponse: $riderLinkVersion")
        }
    }

    private val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHidedebugBinding.inflate(inflater, container, false)

        initPreferences()
        initViews()
        setupClickListeners()
        initServices()
        initTheme()

        return binding.root
    }

    private fun initPreferences() {
        preferences = context?.getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE)

        val logcatEnabled = preferences?.getBoolean(KEY_LOGCAT_SWITCH, false) == true
        val simulationEnabled = preferences?.getBoolean(KEY_SIMULATION_SWITCH, false) == true

        binding.btnSwitchLoge.isChecked = logcatEnabled
        binding.btnSwitchNavi.isChecked = simulationEnabled
    }

    private fun initViews() {
        // 初始化视图状态
    }

    private fun setupClickListeners() {
        with(binding) {
            llBack.setOnClickListener { popBackStack() }

            // 导航点击事件
            itemApp.setOnClickListener {
                navigate(R.id.action_hidedebugFragment_to_appversionFragment)
            }

            itemRiderlink.setOnClickListener {
                val bundle = Bundle().apply {
                    putString("param", riderLinkVersion)
                }
                navigate(R.id.action_hidedebugFragment_to_riderlinkversionFragment, bundle)
            }

            itemAutolink.setOnClickListener {
                navigate(R.id.action_hidedebugFragment_to_autolinkversionFragment)
            }

            itemProductkey.setOnClickListener {
                navigate(R.id.action_hidedebugFragment_to_productkeyFragment)
            }

            itemUuid.setOnClickListener {
                navigate(R.id.action_hidedebugFragment_to_uuidFragment)
            }

            itemCrash.setOnClickListener {
                navigateToLogcat("crash")
            }

            itemLogcat.setOnClickListener {
                navigateToLogcat("logcat")
            }

            // 开关点击事件
            btnSwitchLoge.setOnClickListener {
                handleLogcatSwitchToggle()
            }

            btnSwitchNavi.setOnClickListener {
                handleNaviSwitchToggle()
            }
        }
    }

    private fun initServices() {
        RiderLink.instance.addConnectCallback(serviceCallback)
        RiderService.instance.sendMessageToRiderService(NaviVersionRequest())
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    private fun navigateToLogcat(param: String) {
        val bundle = Bundle().apply {
            putString("param", param)
        }
        navigate(R.id.action_hidedebugFragment_to_logcatFragment, bundle)
    }

    private fun handleLogcatSwitchToggle() {
        preferences?.edit {
            putBoolean(KEY_LOGCAT_SWITCH, binding.btnSwitchLoge.isChecked)
        }

        if (binding.btnSwitchLoge.isChecked) {
            startLogcatManager()
        } else {
            stopLogcatManager()
        }
    }

    private fun handleNaviSwitchToggle() {
        preferences?.edit {
            putBoolean(KEY_SIMULATION_SWITCH, binding.btnSwitchNavi.isChecked)
        }
        RiderLink.instance.setNaviType(binding.btnSwitchNavi.isChecked)
    }

    private fun startLogcatManager() {
        activity?.let { LogcatHelper.getInstance(it.applicationContext).start() }
    }

    private fun stopLogcatManager() {
        activity?.let { LogcatHelper.getInstance(it.applicationContext).stop() }
    }

    private fun initTheme() {
        with(binding) {
            setupBackButton(ibBack)
            hideRoot.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
            setThemeTextColor(
                ThemeColors.PRIMARY_TEXT,
                tvTitle, tvApp, tvRiderlink, tvAutolink,
                tvProductkey, tvUuid, tvCrash, tvLogcat,
                tvLogcatSw, tvSimulationSw
            )
            listOf(
                backgroundApp, backgroundRiderlink, backgroundAutolink,
                backgroundProductkey, backgroundUuid, backgroundCrash,
                backgroundLogcat, backgroundLogcatSw, backgroundSimulationSw
            ).forEach { background ->
                background.setBackgroundResource(
                    ThemeManager.getCurrentThemeRes(requireContext(), ThemeIcons.ITEM_BACKGROUND_S)
                )
            }
        }
    }

    private fun getThemeColor(lightColor: String, darkColor: String): Int {
        return ThemeColors.PRIMARY_TEXT.getCurrentColorInt(requireContext())
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cleanupResources()
        _binding = null
    }

    private fun cleanupResources() {
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        RiderLink.instance.removeConnectCallback(serviceCallback)
    }

    companion object {
        private const val TAG = "HideDebugFragment"
        private const val DEFAULT_VERSION_TEXT = "请在连接平台后查看"

        // SharedPreferences 相关常量
        private const val PREFERENCE_NAME = "config_auto_connection"
        private const val KEY_LOGCAT_SWITCH = "logcat_sw"
        private const val KEY_SIMULATION_SWITCH = "simulation_sw"

        // 主题颜色常量
        private const val COLOR_BACKGROUND_LIGHT = "#FFFFFF"
        private const val COLOR_BACKGROUND_DARK = "#2A3042"
        private const val COLOR_TEXT_LIGHT = "#202229"
        private const val COLOR_TEXT_DARK = "#FFFFFF"
    }
}