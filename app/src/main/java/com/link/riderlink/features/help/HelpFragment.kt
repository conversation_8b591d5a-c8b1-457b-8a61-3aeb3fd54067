package com.link.riderlink.features.help

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import com.link.riderlink.databinding.FragmentHelpBinding
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.theme.ThemeColors
import com.link.riderlink.ui.theme.setupBackButton
import com.link.riderlink.ui.theme.setThemeBackgroundColor
import com.link.riderlink.ui.theme.setThemeTextColor
import com.link.riderlink.ui.extensions.isChina
import com.link.riderlink.ui.extensions.popBackStack

class HelpFragment : Fragment() {
    private var _binding: FragmentHelpBinding? = null
    private val binding get() = _binding!!

    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHelpBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val webSettings: WebSettings = binding.wbHelp.settings
        webSettings.javaScriptEnabled = true
        webSettings.useWideViewPort = true
        webSettings.loadWithOverviewMode = true
        webSettings.setSupportZoom(true)
        binding.wbHelp.loadUrl(HELP_CN_URL)
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    fun initTheme() {
        setupBackButton(binding.ibBack)
        binding.helpRoot.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        binding.tvTitle.setThemeTextColor(ThemeColors.PRIMARY_TEXT)
        binding.wbHelp.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)

        if (requireContext().isChina()) {
            binding.wbHelp.loadUrl(ThemeManager.autoChangeStr(HELP_CN_URL, HELP_CN_URL_NIGHT))
        } else {
            binding.wbHelp.loadUrl(ThemeManager.autoChangeStr(HELP_URL, HELP_URL_NIGHT))
        }
    }

    val themeCallback = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }

    companion object {
        private const val HELP_URL = "file:///android_asset/help.html"
        private const val HELP_URL_NIGHT = "file:///android_asset/help_night.html"
        private const val HELP_CN_URL = "file:///android_asset/help_cn.html"
        private const val HELP_CN_URL_NIGHT = "file:///android_asset/help_cn_night.html"
    }
}