package com.link.riderlink.features.hide

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.link.riderlink.databinding.FragmentProductkeyBinding
import com.link.riderlink.ui.theme.ThemeHelper
import com.link.riderlink.ui.theme.ThemeManager
import com.link.riderlink.ui.extensions.popBackStack
import com.link.riderlink.ui.theme.setupBackButton
import com.link.riderservice.api.RiderService
import kotlinx.coroutines.launch

/**
 * ProductKey显示Fragment
 * 功能：显示产品密钥，支持复制到剪贴板
 */
class ProductKeyFragment : Fragment() {

    companion object {
        private const val TAG = "ProductKeyFragment"

        // 错误信息常量
        private const val ERROR_PRODUCT_KEY_UNAVAILABLE = "产品密钥暂时不可用"
        private const val SUCCESS_COPIED_TO_CLIPBOARD = "已复制到剪贴板"
    }

    private var _binding: FragmentProductkeyBinding? = null
    private val binding get() = _binding!!

    // 产品密钥缓存
    private var productKey: String? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        setupBackPressHandler()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProductkeyBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initializeFragment()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cleanup()
    }

    // ==================== 初始化方法 ====================

    /**
     * 设置返回按键处理
     */
    private fun setupBackPressHandler() {
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    /**
     * 初始化Fragment
     */
    private fun initializeFragment() {
        setupClickListeners()
        loadProductKey()
        initTheme()
        ThemeManager.registerThemeChangeListener(themeChangeListener)
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        binding.apply {
            llBack.setOnClickListener { popBackStack() }

            // 长按复制产品密钥
            txProductkey.setOnLongClickListener {
                copyProductKeyToClipboard()
                true
            }
        }
    }

    /**
     * 清理资源
     */
    private fun cleanup() {
        ThemeManager.unregisterThemeChangeListener(themeChangeListener)
        _binding = null
    }

    // ==================== 数据加载方法 ====================

    /**
     * 加载产品密钥
     */
    private fun loadProductKey() {
        lifecycleScope.launch {
            try {
                productKey = getProductKeyFromService()
                updateProductKeyDisplay()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load product key", e)
                handleProductKeyError()
            }
        }
    }

    /**
     * 从服务获取产品密钥
     */
    private fun getProductKeyFromService(): String? {
        return try {
            val key = RiderService.instance.getProductKey()
            if (key.isBlank()) {
                Log.w(TAG, "Product key is null or empty")
                null
            } else {
                Log.d(TAG, "Product key loaded successfully")
                key
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting product key from service", e)
            null
        }
    }

    /**
     * 更新产品密钥显示
     */
    private fun updateProductKeyDisplay() {
        binding.txProductkey.text = productKey ?: ERROR_PRODUCT_KEY_UNAVAILABLE
    }

    /**
     * 处理产品密钥加载错误
     */
    private fun handleProductKeyError() {
        binding.txProductkey.text = ERROR_PRODUCT_KEY_UNAVAILABLE
        productKey = null
    }

    // ==================== 功能方法 ====================

    /**
     * 复制产品密钥到剪贴板
     */
    private fun copyProductKeyToClipboard() {
        val keyToCopy = productKey
        if (keyToCopy.isNullOrBlank()) {
            showToast(ERROR_PRODUCT_KEY_UNAVAILABLE)
            return
        }

        try {
            val clipboardManager =
                requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
            if (clipboardManager != null) {
                val clipData = ClipData.newPlainText("Product Key", keyToCopy)
                clipboardManager.setPrimaryClip(clipData)
                showToast(SUCCESS_COPIED_TO_CLIPBOARD)
                Log.d(TAG, "Product key copied to clipboard")
            } else {
                Log.e(TAG, "ClipboardManager is null")
                showToast("复制失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to copy to clipboard", e)
            showToast("复制失败")
        }
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    // ==================== 主题管理 ====================

    /**
     * 初始化主题
     */
    private fun initTheme() {
        setupBackButton(binding.ibBack)
        ThemeHelper.setVersionPageTheme(
            binding.productkeyRoot,
            binding.tvTitle,
            binding.txProductkey
        )
    }

    /**
     * 主题变化监听器
     */
    private val themeChangeListener = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }
}