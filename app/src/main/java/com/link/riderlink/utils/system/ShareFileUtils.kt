package com.link.riderlink.utils.system

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.StrictMode
import android.os.StrictMode.VmPolicy
import android.text.TextUtils
import android.util.Log
import androidx.core.content.FileProvider
import com.link.riderlink.features.hide.logcat.LogcatItem
import java.io.File

object ShareFileUtils {
    /**
     * 分享文本
     *
     * @param context
     * @param contentToShare
     */
    fun shareUrl(context: Context, contentToShare: String?) {
        if (TextUtils.isEmpty(contentToShare)) {
            return
        }
        checkFileUriExposure()
        val shareIntent = Intent(Intent.ACTION_SEND)
        shareIntent.putExtra(Intent.EXTRA_TEXT, contentToShare)
        shareIntent.type = "text/plain"
        context.startActivity(Intent.createChooser(shareIntent, "分享APP"))
    }

    /**
     * 分享文件
     *
     * @param context
     * @param contentToShare
     */
    fun shareFile(context: Context, contentToShare: String?) {
        if (TextUtils.isEmpty(contentToShare)) {
            return
        }
        Log.e("shareFile", "shareFile: $contentToShare")
        checkFileUriExposure()
        val intent = Intent(Intent.ACTION_SEND)
        val contentUri =
            FileProvider.getUriForFile(
                context,
                context.packageName + ".fileprovider",
                File(contentToShare)
            )
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        intent.putExtra(Intent.EXTRA_STREAM, contentUri) //传输图片或者文件 采用流的方式
        intent.type = "*/*" //分享文件
        context.startActivity(Intent.createChooser(intent, "分享"))
    }

    /**
     * 分享多个文件
     *
     * @param context
     * @param path
     */
    fun shareFiles(context: Context, items: List<LogcatItem>) {
        val files = ArrayList<Uri>()
        items.forEach { item ->
            val contentUri = FileProvider.getUriForFile(
                context,
                context.packageName + ".fileprovider",
                File(item.filepath)
            )
            files.add(contentUri)
        }
        val intent = Intent(Intent.ACTION_SEND_MULTIPLE) //发送多个文件

        intent.type = "*/*" //多个文件格式

        intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, files) //Intent.EXTRA_STREAM同于传输文件流

        context.startActivity(intent)
    }

    /**
     * 检测手机是否安装某个应用
     *
     * @param context
     * @param appPackageName 应用包名
     * @return true-安装，false-未安装
     */
    fun isAppInstall(context: Context, appPackageName: String): Boolean {
        val packageManager = context.packageManager // 获取packagemanager
        val installedPackages = packageManager.getInstalledPackages(0) // 获取所有已安装程序的包信息
        for (i in installedPackages.indices) {
            val packageName = installedPackages[i].packageName
            if (appPackageName == packageName) {
                return true
            }
        }
        return false
    }

    /**
     * 分享前必须执行本代码，主要用于兼容SDK18以上的系统
     */
    private fun checkFileUriExposure() {
        val builder = VmPolicy.Builder()
        StrictMode.setVmPolicy(builder.build())
        builder.detectFileUriExposure()
    }
}