package com.link.riderlink.utils.connectivity

import android.annotation.SuppressLint
import com.link.riderservice.core.utils.logging.logD
import java.util.Locale
import kotlin.math.abs
import kotlin.math.pow

class BTDistanceUtil {
    companion object {
        const val RSSI_1M: Double = -50.0//发射端和接收端相隔1米时的信号强度
        const val N_VALUE: Double = 6.0//环境衰减因子

        /**
         * 根据rssi值估算出距离
         * @param rssi 信号强度
         * @return 距离(单位：米)
         */
        private fun getDistance(rssi: Int): Double {
            val rssiAbs = abs(rssi)
            val power = (rssiAbs - abs(RSSI_1M)) / (10 * N_VALUE)
            return 10.0.pow(power)
        }

        @SuppressLint("DefaultLocale")
        fun getDistanceSmart(filteredRssi: Int): String {
            if (filteredRssi > RSSI_1M) {
                return "< 1.0 m"
            } else {
                val distance = getDistance(filteredRssi)
                return String.format("%.2f m", distance, Locale.getDefault())
            }
        }
    }
}