package com.link.riderlink.utils.connectivity

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build


/**
 * 判断当前是否有网络连接（基础连接检查，不验证是否能真正上网）
 * 使用现代化的NetworkCapabilities API，检查设备是否连接到网络
 * 注意：即使网络无法访问互联网（如连接到没有上网能力的WiFi），此方法也会返回true
 * @param context 上下文
 * @return true表示设备已连接到网络（WiFi/移动数据/以太网），false表示无网络连接
 */
fun isNetConnection(context: Context?): Boolean {
    context ?: return false

    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        ?: return false

    // 使用现代API (API 23+)
    val activeNetwork = connectivityManager.activeNetwork ?: return false
    val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false

    // 检查网络是否具备基本的传输能力
    val hasTransport = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)

    // 检查网络是否具备互联网访问能力声明（注意：这不保证真正能上网）
    val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)

    return hasTransport && hasInternet
}

/**
 * 判断当前网络是否真正可以访问互联网
 * 这个方法会检查网络是否已通过系统验证，确保真正能够上网
 * @param context 上下文
 * @return true表示网络已验证可以上网，false表示网络无法上网或未连接
 */
fun isNetworkValidated(context: Context?): Boolean {
    context ?: return false
    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        ?: return false

    // 优先获取当前进程绑定的网络
    val boundNetwork =
        connectivityManager.boundNetworkForProcess

    // 判断逻辑：优先检查绑定网络，其次检查活动网络
    val networkToCheck = boundNetwork ?: connectivityManager.activeNetwork ?: return false

    val networkCapabilities = connectivityManager.getNetworkCapabilities(networkToCheck) ?: return false

    // 检查网络是否已验证（真正能上网）
    return networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
}

/**
 * 灵活的网络连接检查函数
 * @param context 上下文
 * @param requireValidation 是否需要验证网络真正能上网，默认为false
 * @return true表示满足条件的网络连接存在，false表示不满足条件
 */
fun isNetworkAvailable(context: Context?, requireValidation: Boolean = false): Boolean {
    return if (requireValidation) {
        isNetworkValidated(context)
    } else {
        isNetConnection(context)
    }
}

/**
 * 检查是否连接到WiFi网络
 * @param context 上下文
 * @return true表示连接到WiFi，false表示未连接WiFi
 */
fun isWifiConnected(context: Context?): Boolean {
    context ?: return false

    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        ?: return false

    val activeNetwork = connectivityManager.activeNetwork ?: return false
    val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false

    return networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) &&
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
}

/**
 * 检查是否连接到移动数据网络
 * @param context 上下文
 * @return true表示连接到移动数据，false表示未连接移动数据
 */
fun isMobileConnected(context: Context?): Boolean {
    context ?: return false

    val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        ?: return false

    val activeNetwork = connectivityManager.activeNetwork ?: return false
    val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false

    return networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) &&
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
}