package com.link.riderlink.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationManagerCompat
import com.link.riderlink.R
import com.link.riderlink.service.events.MediaProjectionEvent
import org.greenrobot.eventbus.EventBus

class RiderLinkProjectionService : Service() {
    private var mNotificationManager: NotificationManagerCompat? = null
    private var notification: Notification? = null

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        startForegroundService()
        Log.d(TAG, "onStartCommand: $intent")
        val resultCode = intent.getIntExtra("resultCode", -1)
        val data = intent.getParcelableExtra<Intent?>("data")
        val isSecondary = intent.getBooleanExtra("isSecondary", false)
        EventBus.getDefault().post(MediaProjectionEvent(data, resultCode, isSecondary))
        return super.onStartCommand(intent, flags, startId)
    }

    private fun createNotificationChannel() {
        mNotificationManager = NotificationManagerCompat.from(applicationContext)
        val channelOneId = "com.link.riderlink.service.RiderLinkProjectionService"
        val channelName = "RiderLinkProjectionService"
        val notificationChannel: NotificationChannel?
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            notificationChannel = NotificationChannel(
                channelOneId, channelName,
                NotificationManager.IMPORTANCE_LOW
            )
            mNotificationManager!!.createNotificationChannel(notificationChannel)
        }
        val builder: Notification.Builder?
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder = Notification.Builder(this, channelOneId)
        } else {
            builder = Notification.Builder(this)
        }
        notification = builder
            .setContentTitle(getString(R.string.app_name))
            .setContentText(resources.getString(R.string.mirror_context))
            .setSmallIcon(R.mipmap.ic_launcher_foreground).build()
    }

    override fun onDestroy() {
        super.onDestroy()
        mNotificationManager!!.cancelAll()
        stopForeground(true)
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    private fun startForegroundService() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                NOTIFICATION_ID,
                notification!!,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION
            )
        } else {
            startForeground(NOTIFICATION_ID, notification)
        }
    }

    companion object {
        private const val TAG = "RiderLinkProjectionService"
        private const val NOTIFICATION_ID = 0x2345
    }
}