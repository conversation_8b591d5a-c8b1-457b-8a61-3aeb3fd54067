package com.link.riderlink.ui.theme

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.util.LruCache
import com.link.riderlink.ui.theme.ThemeManager.themeMode
import java.util.concurrent.ConcurrentHashMap


object ThemeManager {
    //是否跟随配置
    private var followConfig = true

    // 默认是日间模式
    private var currentThemeMode = ThemeMode.DAY

    // 主题模式监听器 - 使用HashSet提升性能
    private val themeChangeListeners: MutableSet<OnThemeChangeListener> = ConcurrentHashMap.newKeySet()

    // 高性能LRU缓存 - 缓存最近使用的1000个资源映射
    private val resourceCache = LruCache<String, Int>(1000)

    // 缓存Context信息，避免重复获取
    private var cachedPackageName: String? = null

    // 夜间模式资源的后缀，比如日件模式资源名为：R.color.activity_bg, 那么夜间模式就为 ：R.color.activity_bg_night
    private const val RESOURCE_SUFFIX = "_night"

    /**
     * 初始化主题系统
     * 建议在Application.onCreate()中调用，进行系统预热
     */
    fun initialize(enablePerformanceMonitoring: Boolean = false) {
        // 预热静态映射表
        ThemeResourceMap.warmUp()

        // 根据需要启用性能监控
        if (enablePerformanceMonitoring) {
            ThemePerformanceMonitor.enable()
        }
    }

    /**
     * 获取性能统计信息
     * 用于调试和性能分析
     */
    fun getPerformanceReport(): String {
        return ThemePerformanceMonitor.getPerformanceReport()
    }

    /**
     * 清理资源缓存
     * 在内存紧张时可以调用
     */
    fun clearCache() {
        resourceCache.evictAll()
    }

    /**
     * 超高性能版本：根据传入的日间模式的resId得到相应主题的resId
     * 优化点：
     * 1. 优先使用静态映射表（最快）
     * 2. 使用LRU缓存替代嵌套HashMap
     * 3. 集成性能监控
     * 4. 移除所有调试日志
     * 5. 缓存packageName避免重复获取
     *
     * @param dayModeResourceId 日间模式的resId
     * @return 相应主题的resId，若为日间模式，则得到dayResId；反之夜间模式得到nightResId
     */
    fun getCurrentThemeRes(context: Context, dayModeResourceId: Int): Int {
        return ThemePerformanceMonitor.measureTime(ThemePerformanceMonitor.Metrics.RESOURCE_LOOKUP) {
            // 日间模式直接返回，无需查找
            if (themeMode == ThemeMode.DAY) {
                return@measureTime dayModeResourceId
            }

            // 第一优先级：静态映射表查找（最快，O(1)复杂度）
            ThemeResourceMap.getNightResource(dayModeResourceId)?.let { staticNightRes ->
                ThemePerformanceMonitor.recordStaticMapLookup()
                return@measureTime staticNightRes
            }

            // 第二优先级：LRU缓存查找
            val cacheKey = "${dayModeResourceId}$RESOURCE_SUFFIX"
            resourceCache.get(cacheKey)?.let { cachedResId ->
                ThemePerformanceMonitor.recordCacheHit()
                return@measureTime if (cachedResId != 0) cachedResId else dayModeResourceId
            }

            // 第三优先级：动态资源查找（最慢，但有缓存）
            ThemePerformanceMonitor.recordCacheMiss()
            ThemePerformanceMonitor.recordDynamicLookup()
            val nightResId = findNightResource(context, dayModeResourceId)

            // 缓存结果（包括失败的查找，避免重复查找）
            resourceCache.put(cacheKey, nightResId)

            if (nightResId != 0) nightResId else dayModeResourceId
        }
    }

    /**
     * 查找夜间模式资源的优化实现
     * 减少反射调用和异常处理开销
     */
    private fun findNightResource(context: Context, dayResId: Int): Int {
        return try {
            // 缓存packageName，避免重复获取
            if (cachedPackageName == null) {
                cachedPackageName = context.packageName
            }

            val resources = context.resources
            val entryName = resources.getResourceEntryName(dayResId)
            val typeName = resources.getResourceTypeName(dayResId)

            // 使用缓存的packageName
            resources.getIdentifier(
                "$entryName$RESOURCE_SUFFIX",
                typeName,
                cachedPackageName
            )
        } catch (e: Exception) {
            // 静默处理异常，返回0表示未找到
            0
        }
    }

    /**
     * 注册ThemeChangeListener - 优化版本
     * 使用HashSet，O(1)复杂度的add操作
     */
    fun registerThemeChangeListener(listener: OnThemeChangeListener) {
        themeChangeListeners.add(listener)
    }

    /**
     * 反注册ThemeChangeListener - 优化版本
     * 使用HashSet，O(1)复杂度的remove操作
     */
    fun unregisterThemeChangeListener(listener: OnThemeChangeListener) {
        themeChangeListeners.remove(listener)
    }

    /**
     * 根据传入值切换元素ID - 优化版本
     * 移除调试日志，提升性能
     */
    fun autoChangeInt(dayModeResourceId: Int, nightModeResourceId: Int): Int {
        return if (themeMode == ThemeMode.DAY) {
            dayModeResourceId
        } else {
            nightModeResourceId
        }
    }

    /**
     * 根据传入值切换元素ID
     *
     * @param dayModeString
     *
     * @param nightModeString
     */
    fun autoChangeStr(dayModeString: String, nightModeString: String): String {
        return if (themeMode == ThemeMode.DAY) {
            dayModeString
        } else {
            nightModeString
        }
    }

    fun showBar(show: Boolean, barColor: Int) {
        if (themeChangeListeners.isNotEmpty()) {
            for (listener in themeChangeListeners) {
                listener.onThemeBarChanged(show, barColor)
            }
        }
    }

    var themeMode: ThemeMode
        /**
         * 得到主题模式
         *
         * @return
         */
        get() = currentThemeMode
        /**
         * 设置主题模式 - 优化版本
         * 移除调试日志，优化监听器通知
         */
        set(themeMode) {
            if (currentThemeMode != themeMode) {
                currentThemeMode = themeMode
                // 清空缓存，确保主题切换后使用新的资源
                resourceCache.evictAll()
                // 通知所有监听器
                themeChangeListeners.forEach { listener ->
                    listener.onThemeChanged()
                }
            }
        }

    /**
     * 主题模式，分为日间模式和夜间模式
     */
    enum class ThemeMode {
        DAY, NIGHT
    }

    /**
     * 主题模式切换监听器
     */
    abstract class OnThemeChangeListener {
        /**
         * 主题切换时回调
         */
        open fun onThemeChanged() {}

        /**
         * 主题导航状态栏改变
         */
        open fun onThemeBarChanged(show: Boolean, barColor: Int) {}
    }

    /**
     * 判断系统是否是深色模式
     * @return
     */
    fun isSystemNightMode(context: Context): Boolean {
        try {
            val uiMode = context.resources.configuration.uiMode
            val nightModeMask = Configuration.UI_MODE_NIGHT_MASK
            val currentNightModeSetting = uiMode.and(nightModeMask)
            return (currentNightModeSetting == Configuration.UI_MODE_NIGHT_YES)
        } catch (_: Exception) {

        }
        return false;
    }

    /**
     * 判断主题是否是深色模式
     * @return
     */
    fun isThemeNightMode(): Boolean {
        return themeMode == ThemeMode.NIGHT
    }

    /**
     * 判断是否是深色模式
     * @return
     */
    fun isNightMode(context: Context): Boolean {
        return isThemeNightMode()
    }

    fun onConfigurationChanged(context: Context, isNightModeActive: Boolean) {
        val themePreferences =
            context.getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        val isNightModeEnabled =
            themePreferences?.getBoolean("mode_night", false) == true
        val isFollowSystemEnabled =
            themePreferences?.getBoolean("follow_system", false) == true
        if (isNightModeEnabled && isFollowSystemEnabled) {
            themeMode = if (isNightModeActive) ThemeMode.NIGHT else ThemeMode.DAY
        } else if (isNightModeEnabled) {
            themeMode = ThemeMode.NIGHT
        }
    }
}