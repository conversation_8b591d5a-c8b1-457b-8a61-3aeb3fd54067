package com.link.riderlink.ui.components.home

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.HorizontalScrollView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.transition.TransitionManager
import com.link.riderlink.R
import me.jessyan.autosize.utils.AutoSizeUtils

class MainToolView1 @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0,
    onNavigate: () -> Unit = {},
    onGoDvr: () -> Unit = {},
    val goDvr: () -> Unit = {},
) : HorizontalScrollView(context, attrs, defStyle) {
    private val mChildWidth = AutoSizeUtils.mm2px(context, 300f)
    private val mChildHeight = AutoSizeUtils.mm2px(context, 258f)

    private val navi = MainToolItemInfoNew(
        context = context,
        title = "我要导航",
        detail = "内置导航 一键出发",
        resId = com.link.riderdvr.R.drawable.connect_btn_selector
    ) {
        onNavigate()
    }
    private val dvr = MainToolItemInfoNew(
        context = context,
        title = "查看dvr",
        detail = "记录美好 精彩重现",
        resId = com.link.riderdvr.R.drawable.manual_btn_selector
    ) {
        onGoDvr()
    }

    private val qq = MainToolItemInfoNew(
        context = context,
        title = "QQ音乐",
        detail = "边走边听 快乐加倍",
        resId = com.link.riderdvr.R.drawable.dvr_btn_selector
    ) {
        goDvr()
    }

    private val mContainer: ConstraintLayout

    init {
        id = ViewCompat.generateViewId()
        mContainer = ConstraintLayout(context)
        val set = ConstraintSet().apply {
            constrainWidth(navi.id, mChildWidth)
            constrainHeight(navi.id, mChildHeight)
            connect(
                navi.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START,
                AutoSizeUtils.mm2px(context, 68f)
            )

            constrainWidth(dvr.id, mChildWidth)
            constrainHeight(dvr.id, mChildHeight)
            connect(
                dvr.id,
                ConstraintSet.START,
                navi.id,
                ConstraintSet.END,
                AutoSizeUtils.mm2px(context, 22f)
            )

            constrainWidth(qq.id, mChildWidth)
            constrainHeight(qq.id, mChildHeight)
            connect(
                qq.id,
                ConstraintSet.START,
                <EMAIL>,
                ConstraintSet.END,
                AutoSizeUtils.mm2px(context, 22f)
            )
        }
        mContainer.addView(navi)
        mContainer.addView(dvr)
        mContainer.addView(qq)
        TransitionManager.beginDelayedTransition(mContainer)
        set.applyTo(mContainer)
        addView(
            mContainer,
            LayoutParams.WRAP_CONTENT,
            LayoutParams.MATCH_PARENT
        )

    }


    companion object {
        private const val TAG = "ConnectDetailInfo"
    }
}

class MainToolItemInfoNew @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0,
    title: CharSequence = "",
    detail: CharSequence = "",
    @DrawableRes resId: Int = 0,
    click: (view: View) -> Unit = {}
) : ConstraintLayout(context, attrs, defStyle) {

    val texttitle = TextView(context).apply {
        id = generateViewId()
        text = title
        textSize = AutoSizeUtils.pt2px(context, 35f).toFloat()
        setTextColor(ContextCompat.getColor(context, R.color.home_connect_info))
    }

    val textdetail = TextView(context).apply {
        id = generateViewId()
        text = detail
        textSize = AutoSizeUtils.pt2px(context, 35f).toFloat()
        setTextColor(ContextCompat.getColor(context, R.color.home_connect_info))
    }

    val set = ConstraintSet().apply {
        constrainWidth(texttitle.id, LayoutParams.WRAP_CONTENT)
        constrainHeight(texttitle.id, LayoutParams.WRAP_CONTENT)
        connect(texttitle.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
        connect(texttitle.id, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
        connect(
            texttitle.id,
            ConstraintSet.BOTTOM,
            ConstraintSet.PARENT_ID,
            ConstraintSet.BOTTOM,
            AutoSizeUtils.mm2px(context, 37f)
        )
        connect(
            textdetail.id,
            ConstraintSet.TOP,
            texttitle.id,
            ConstraintSet.BOTTOM,
            AutoSizeUtils.mm2px(context, 37f)
        )
    }

    init {
        id = generateViewId()
        setBackgroundResource(resId)
        addView(texttitle)
        TransitionManager.beginDelayedTransition(this)
        set.applyTo(this)
        setOnClickListener {
            click(it)
        }
    }
}