package com.link.riderlink.ui.components.home

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import com.link.riderlink.R

class MainConnectView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {
    fun setBackgroundImage(backgroundImageView: Int) {
        findViewById<ImageView>(R.id.main_connect_background).setImageResource(backgroundImageView)
    }

    fun setBitmap(bitmapOnButton: Int) {
        findViewById<ImageView>(R.id.main_connect_bitmap).setImageResource(bitmapOnButton)
    }

    fun setText(textOnButton: String) {
        findViewById<TextView>(R.id.main_connect_text).text = textOnButton
    }

    fun setTextColor(colorId: Int) {
        findViewById<TextView>(R.id.main_connect_text).setTextColor(
            this.resources.getColor(colorId, null)
        )
    }

    init {
        //从xml的属性中获取到字体颜色与string
        LayoutInflater.from(context).inflate(R.layout.main_connect_button, this)
        val ta = context.obtainStyledAttributes(attrs, R.styleable.MainConnectAttrs);
        val backgroundImageView =
            ta.getResourceId(R.styleable.MainConnectAttrs_MainConnectBackground, 0)
        val bitmapOnButton = ta.getResourceId(R.styleable.MainConnectAttrs_MainConnectBitmap, 0)
        val textOnButton = ta.getString(R.styleable.MainConnectAttrs_MainConnectText)
        val textColor = ta.getColor(R.styleable.MainConnectAttrs_MainConnectTextColor, Color.WHITE)
        findViewById<ImageView>(R.id.main_connect_background).setBackgroundResource(
            backgroundImageView
        )
        findViewById<ImageView>(R.id.main_connect_bitmap).setImageResource(bitmapOnButton)
        findViewById<TextView>(R.id.main_connect_text).text = textOnButton
        findViewById<TextView>(R.id.main_connect_text).setTextColor(textColor)
    }

    companion object {
        private const val TAG = "MainConnectView"
    }
}
