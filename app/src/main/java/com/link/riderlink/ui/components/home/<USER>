package com.link.riderlink.ui.components.home

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.link.riderlink.R
import com.link.riderlink.ui.theme.ThemeManager

@SuppressLint("CutPasteId")
class MainToolView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {

    var mBitmapId: Int = 0
    fun setBitImage(backgroundImageView: Int) {
        mBitmapId = backgroundImageView
        findViewById<ImageView>(R.id.main_tool_bitmap).setImageResource(
            ThemeManager.getCurrentThemeRes(
                context,
                mBitmapId
            )
        )
    }

    fun settitle(bitmapOnButton: Int) {
        findViewById<ImageView>(R.id.main_tool_title).setImageResource(bitmapOnButton)
    }

    fun setText(textOnButton: String) {
        findViewById<TextView>(R.id.main_tool_text).text = textOnButton
    }

    fun setTextColor(colorId: Int) {
        findViewById<TextView>(R.id.main_tool_text).setTextColor(
            this.resources.getColor(
                colorId,
                null
            )
        )
    }

    fun setTitleColor(colorId: Int) {
        findViewById<TextView>(R.id.main_tool_title).setTextColor(
            this.resources.getColor(
                colorId,
                null
            )
        )
    }

    fun isClickAble(able: Boolean) {
        findViewById<ConstraintLayout>(R.id.main_tool_button_root).isClickable = able
    }

    fun onThemeChange(isNight: Boolean, context: Context) {
        if (!isNight) {
            findViewById<TextView>(R.id.main_tool_text).setTextColor(
                resources.getColor(
                    R.color.black_tx,
                    null
                )
            )
            findViewById<TextView>(R.id.main_tool_title).setTextColor(
                resources.getColor(
                    R.color.black_tx,
                    null
                )
            )
        } else {
            findViewById<TextView>(R.id.main_tool_text).setTextColor(
                resources.getColor(
                    com.link.riderdvr.R.color.white,
                    null
                )
            )
            findViewById<TextView>(R.id.main_tool_title).setTextColor(
                resources.getColor(
                    R.color.grey_tx,
                    null
                )
            )
        }
        findViewById<ConstraintLayout>(R.id.main_tool_button_root).setBackgroundResource(
            ThemeManager.getCurrentThemeRes(
                context,
                R.drawable.main_tool_background_select
            )
        )
        findViewById<ImageView>(R.id.main_tool_bitmap).setImageResource(
            ThemeManager.getCurrentThemeRes(
                context,
                mBitmapId
            )
        )
    }

    init {
        //从xml的属性中获取到字体颜色与string
        LayoutInflater.from(context).inflate(R.layout.main_tool_button, this)
        val ta = context.obtainStyledAttributes(attrs, R.styleable.MainToolAttrs)
        mBitmapId = ta.getResourceId(R.styleable.MainToolAttrs_MainToolBitmap, 0)
        val textContent = ta.getString(R.styleable.MainToolAttrs_MainToolText)
        val textTitle = ta.getString(R.styleable.MainToolAttrs_MainToolTitle)
        val textColor = ta.getColor(R.styleable.MainToolAttrs_MainToolTextColor, Color.WHITE)
        val titleColor = ta.getColor(R.styleable.MainToolAttrs_MainToolTitleColor, Color.WHITE)
        findViewById<ImageView>(R.id.main_tool_bitmap).setImageResource(mBitmapId)
        findViewById<TextView>(R.id.main_tool_text).text = textContent
        findViewById<TextView>(R.id.main_tool_title).text = textTitle
        findViewById<TextView>(R.id.main_tool_text).setTextColor(textColor)
        findViewById<TextView>(R.id.main_tool_title).setTextColor(titleColor)
        findViewById<TextView>(R.id.main_tool_text).textSize = 12F
        findViewById<TextView>(R.id.main_tool_title).textSize = 16F
    }

    companion object {
        private const val TAG = "ConnectInfoView"
    }
}
