package com.link.riderlink.ui.theme

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.fragment.app.Fragment
import com.link.riderlink.R
import com.link.riderlink.ui.extensions.popBackStack

/**
 * 主题设置扩展函数
 * 简化View的主题颜色设置，减少重复代码
 */

// TextView主题颜色设置扩展
fun TextView.setThemeTextColor(colorPair: ThemeColors.ColorPair) {
    this.setTextColor(colorPair.getCurrentColorInt(this.context))
}

// View背景颜色设置扩展  
fun View.setThemeBackgroundColor(colorPair: ThemeColors.ColorPair) {
    this.setBackgroundColor(colorPair.getCurrentColorInt(this.context))
}

// 多个TextView批量设置相同主题颜色
fun setThemeTextColor(colorPair: ThemeColors.ColorPair, vararg textViews: TextView) {
    textViews.forEach { it.setThemeTextColor(colorPair) }
}

// 多个View批量设置相同背景颜色
fun setThemeBackgroundColor(colorPair: ThemeColors.ColorPair, vararg views: View) {
    views.forEach { it.setThemeBackgroundColor(colorPair) }
}

/**
 * 通用主题设置器
 * 为Fragment提供统一的主题设置方法
 */
object ThemeHelper {
    
    /**
     * 设置标准页面主题（背景 + 标题）
     */
    fun setStandardPageTheme(rootView: View, titleView: TextView) {
        rootView.setThemeBackgroundColor(ThemeColors.PRIMARY_BACKGROUND)
        titleView.setThemeTextColor(ThemeColors.PRIMARY_TEXT)
    }
    
    /**
     * 设置版本信息页面主题
     */
    fun setVersionPageTheme(rootView: View, titleView: TextView, contentView: TextView) {
        rootView.setThemeBackgroundColor(ThemeColors.VERSION_BACKGROUND) 
        setThemeTextColor(ThemeColors.VERSION_TEXT, titleView, contentView)
    }
    
    /**
     * 设置设置页面所有文字颜色
     */
    fun setSettingPageTextTheme(vararg textViews: TextView) {
        setThemeTextColor(ThemeColors.PRIMARY_TEXT, *textViews)
    }
}

/**
 * 图标资源扩展函数 - 简化重复的getCurrentThemeRes调用
 */

/**
 * 为ImageView设置主题相关的图标资源
 */
fun ImageView.setThemeImageResource(@DrawableRes resId: Int) {
    setImageResource(ThemeManager.getCurrentThemeRes(context, resId))
}

/**
 * 为View设置主题相关的背景资源
 */
fun View.setThemeBackgroundResource(@DrawableRes resId: Int) {
    setBackgroundResource(ThemeManager.getCurrentThemeRes(context, resId))
}

/**
 * 完整的图标资源常量 - 覆盖项目中所有高频使用的图标
 */
object ThemeIcons {
    // 导航图标
    val BACK_ICON = R.drawable.set_back
    val ROUTE_ICON = R.drawable.item_route
    
    // 设置页面图标
    val MOON_ICON = R.drawable.item_moon
    val HELP_ICON = R.drawable.circle_help
    val EDIT_ICON = R.drawable.edit_alt
    val INFO_ICON = R.drawable.circle_information
    val AUDIO_ICON = R.drawable.bluetooth_audio
    
    // 背景资源
    val ITEM_BACKGROUND_D = R.drawable.item_background_d
    val ITEM_BACKGROUND_S = R.drawable.item_background_s
    val SEARCH_BACKGROUND = R.drawable.search_background
    
    // 对话框相关
    val CONNECT_DIALOG_BACKGROUND = R.drawable.connect_dialog_background
    val DELETE_BTN_CANCEL = R.drawable.delete_btn_cancel
    val DELETE_BTN_OK = R.drawable.delete_btn_ok
    
    // DVR相关图标
    val WAIT_COPY_SELECTOR = com.link.riderdvr.R.drawable.wait_copy_selector
    val WAIT_DOWNLOAD = com.link.riderdvr.R.drawable.wait_download
}

/**
 * Fragment返回按钮的快速设置 - 最常用的模式
 */
fun Fragment.setupBackButton(imageView: ImageView, onClick: () -> Unit = { popBackStack() }) {
    imageView.setThemeImageResource(ThemeIcons.BACK_ICON)
    imageView.setOnClickListener { onClick() }
}

/**
 * ImageView快速设置主题图标 - 使用常量简化调用
 */
fun ImageView.setThemeIcon(iconConstant: Int) {
    setThemeImageResource(iconConstant)
}

/**
 * ThemeResourceHelper - 批量资源设置工具
 */
object ThemeResourceHelper {
    
    /**
     * 设置页面图标批量设置 - 将8行代码简化为1行
     */
    fun setSettingPageIcons(
        nightIcon: ImageView,
        helpIcon: ImageView, 
        privateIcon: ImageView,
        aboutIcon: ImageView,
        delIcon: ImageView
    ) {
        nightIcon.setThemeIcon(ThemeIcons.MOON_ICON)
        helpIcon.setThemeIcon(ThemeIcons.HELP_ICON)
        privateIcon.setThemeIcon(ThemeIcons.EDIT_ICON)
        aboutIcon.setThemeIcon(ThemeIcons.INFO_ICON)
        delIcon.setThemeIcon(ThemeIcons.AUDIO_ICON)
    }
    
    /**
     * 设置页面背景批量设置
     */
    fun setSettingPageBackgrounds(vararg backgrounds: View) {
        backgrounds.forEachIndexed { index, view ->
            val backgroundRes = if (index < 2) {
                ThemeIcons.ITEM_BACKGROUND_D
            } else {
                ThemeIcons.ITEM_BACKGROUND_S
            }
            view.setThemeBackgroundResource(backgroundRes)
        }
    }
    
    /**
     * DVR适配器图标设置
     */
    fun setDvrAdapterIcons(copyButton: ImageView, downloadButton: ImageView) {
        copyButton.setThemeIcon(ThemeIcons.WAIT_COPY_SELECTOR)
        downloadButton.setThemeIcon(ThemeIcons.WAIT_DOWNLOAD)
    }
    
    /**
     * 对话框资源批量设置
     */
    fun setDialogResources(
        dialogBackground: View,
        cancelButton: View,
        okButton: View
    ) {
        dialogBackground.setThemeBackgroundResource(ThemeIcons.CONNECT_DIALOG_BACKGROUND)
        cancelButton.setThemeBackgroundResource(ThemeIcons.DELETE_BTN_CANCEL)
        okButton.setThemeBackgroundResource(ThemeIcons.DELETE_BTN_OK)
    }
    
    /**
     * 通用批量图标设置
     */
    fun setIconsFromConstants(vararg iconPairs: Pair<ImageView, Int>) {
        iconPairs.forEach { (imageView, iconConstant) ->
            imageView.setThemeIcon(iconConstant)
        }
    }
    
    /**
     * 批量获取主题资源ID
     */
    fun getThemeResources(context: Context, vararg drawableResIds: Int): List<Int> {
        return drawableResIds.map { ThemeManager.getCurrentThemeRes(context, it) }
    }
}
