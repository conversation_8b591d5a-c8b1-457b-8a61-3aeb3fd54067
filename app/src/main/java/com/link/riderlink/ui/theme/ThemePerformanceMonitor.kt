package com.link.riderlink.ui.theme

import android.util.Log
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.atomic.AtomicInteger

/**
 * 主题系统性能监控器
 * 
 * 用于监控主题切换和资源查找的性能指标
 * 帮助识别性能瓶颈和验证优化效果
 * 
 * 使用方式：
 * ThemePerformanceMonitor.startTiming("resource_lookup")
 * // 执行操作
 * ThemePerformanceMonitor.endTiming("resource_lookup")
 */
object ThemePerformanceMonitor {
    
    private const val TAG = "ThemePerformance"
    private var isEnabled = false // 默认关闭，避免生产环境性能影响
    
    // 性能统计数据
    private val timingMap = mutableMapOf<String, Long>()
    private val callCountMap = mutableMapOf<String, AtomicInteger>()
    private val totalTimeMap = mutableMapOf<String, AtomicLong>()
    
    // 预定义的监控项
    object Metrics {
        const val RESOURCE_LOOKUP = "resource_lookup"
        const val CACHE_HIT = "cache_hit"
        const val CACHE_MISS = "cache_miss"
        const val THEME_SWITCH = "theme_switch"
        const val LISTENER_NOTIFY = "listener_notify"
        const val STATIC_MAP_LOOKUP = "static_map_lookup"
        const val DYNAMIC_LOOKUP = "dynamic_lookup"
    }
    
    /**
     * 启用性能监控
     * 建议只在调试模式下启用
     */
    fun enable() {
        isEnabled = true
        Log.d(TAG, "Theme performance monitoring enabled")
    }
    
    /**
     * 禁用性能监控
     */
    fun disable() {
        isEnabled = false
        Log.d(TAG, "Theme performance monitoring disabled")
    }
    
    /**
     * 开始计时
     */
    fun startTiming(operation: String) {
        if (!isEnabled) return
        timingMap[operation] = System.nanoTime()
    }
    
    /**
     * 结束计时并记录
     */
    fun endTiming(operation: String) {
        if (!isEnabled) return
        
        val startTime = timingMap[operation] ?: return
        val endTime = System.nanoTime()
        val duration = endTime - startTime
        
        // 更新调用次数
        callCountMap.getOrPut(operation) { AtomicInteger(0) }.incrementAndGet()
        
        // 更新总时间
        totalTimeMap.getOrPut(operation) { AtomicLong(0) }.addAndGet(duration)
        
        // 移除临时计时数据
        timingMap.remove(operation)
    }
    
    /**
     * 记录缓存命中
     */
    fun recordCacheHit() {
        if (!isEnabled) return
        callCountMap.getOrPut(Metrics.CACHE_HIT) { AtomicInteger(0) }.incrementAndGet()
    }
    
    /**
     * 记录缓存未命中
     */
    fun recordCacheMiss() {
        if (!isEnabled) return
        callCountMap.getOrPut(Metrics.CACHE_MISS) { AtomicInteger(0) }.incrementAndGet()
    }
    
    /**
     * 记录静态映射表查找
     */
    fun recordStaticMapLookup() {
        if (!isEnabled) return
        callCountMap.getOrPut(Metrics.STATIC_MAP_LOOKUP) { AtomicInteger(0) }.incrementAndGet()
    }
    
    /**
     * 记录动态资源查找
     */
    fun recordDynamicLookup() {
        if (!isEnabled) return
        callCountMap.getOrPut(Metrics.DYNAMIC_LOOKUP) { AtomicInteger(0) }.incrementAndGet()
    }
    
    /**
     * 获取性能统计报告
     */
    fun getPerformanceReport(): String {
        if (!isEnabled) return "Performance monitoring is disabled"
        
        val report = StringBuilder()
        report.appendLine("=== Theme Performance Report ===")
        
        // 计时统计
        report.appendLine("\n--- Timing Statistics ---")
        totalTimeMap.forEach { (operation, totalTime) ->
            val callCount = callCountMap[operation]?.get() ?: 0
            val avgTime = if (callCount > 0) totalTime.get() / callCount else 0
            report.appendLine("$operation: $callCount calls, avg ${avgTime / 1000}μs, total ${totalTime.get() / 1000000}ms")
        }
        
        // 缓存统计
        report.appendLine("\n--- Cache Statistics ---")
        val cacheHits = callCountMap[Metrics.CACHE_HIT]?.get() ?: 0
        val cacheMisses = callCountMap[Metrics.CACHE_MISS]?.get() ?: 0
        val totalCacheAccess = cacheHits + cacheMisses
        val hitRate = if (totalCacheAccess > 0) (cacheHits * 100.0 / totalCacheAccess) else 0.0
        report.appendLine("Cache hits: $cacheHits")
        report.appendLine("Cache misses: $cacheMisses")
        report.appendLine("Hit rate: ${"%.2f".format(hitRate)}%")
        
        // 查找方式统计
        report.appendLine("\n--- Lookup Method Statistics ---")
        val staticLookups = callCountMap[Metrics.STATIC_MAP_LOOKUP]?.get() ?: 0
        val dynamicLookups = callCountMap[Metrics.DYNAMIC_LOOKUP]?.get() ?: 0
        report.appendLine("Static map lookups: $staticLookups")
        report.appendLine("Dynamic lookups: $dynamicLookups")
        
        return report.toString()
    }
    
    /**
     * 打印性能报告到日志
     */
    fun logPerformanceReport() {
        if (!isEnabled) return
        Log.i(TAG, getPerformanceReport())
    }
    
    /**
     * 重置所有统计数据
     */
    fun reset() {
        timingMap.clear()
        callCountMap.clear()
        totalTimeMap.clear()
        Log.d(TAG, "Performance statistics reset")
    }
    
    /**
     * 获取缓存命中率
     */
    fun getCacheHitRate(): Double {
        val cacheHits = callCountMap[Metrics.CACHE_HIT]?.get() ?: 0
        val cacheMisses = callCountMap[Metrics.CACHE_MISS]?.get() ?: 0
        val totalAccess = cacheHits + cacheMisses
        return if (totalAccess > 0) (cacheHits * 100.0 / totalAccess) else 0.0
    }
    
    /**
     * 检查是否启用监控
     */
    fun isEnabled(): Boolean = isEnabled
}

/**
 * 性能监控的扩展函数，简化使用
 */
inline fun <T> ThemePerformanceMonitor.measureTime(operation: String, block: () -> T): T {
    startTiming(operation)
    return try {
        block()
    } finally {
        endTiming(operation)
    }
}
