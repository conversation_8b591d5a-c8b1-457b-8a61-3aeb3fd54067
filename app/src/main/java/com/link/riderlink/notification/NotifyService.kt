package com.link.riderlink.notification

import android.app.Notification
import android.content.ComponentName
import android.content.Intent
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import com.link.riderservice.api.RiderService
import com.link.riderservice.api.dto.NotificationInfo
import com.link.riderservice.core.utils.system.Platform

class NotifyService : NotificationListenerService() {
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }

    override fun onListenerDisconnected() {
        super.onListenerDisconnected()
        requestRebind(ComponentName(this, NotificationListenerService::class.java))
    }

    override fun onNotificationPosted(sbn: StatusBarNotification) {
        super.onNotificationPosted(sbn)
        val from = sbn.packageName
        val extras = sbn.notification.extras
        val title = extras.getString(Notification.EXTRA_TITLE)
        var text = extras.getString(Notification.EXTRA_TEXT)
        if (text?.isEmpty() == true && sbn.notification.tickerText != null) {
            text = sbn.notification.tickerText.toString()
        }
        RiderService.instance.sendMessageToRiderService(
            NotificationInfo(
                appName = from?.let { Platform.getAppName(applicationContext, it) } ?: "",
                title = title ?: "",
                content = text ?: "")
        )
    }
}