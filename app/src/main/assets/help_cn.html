<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>睿连智行帮助</title>
    <script>
        function toggleCollapse(targetId) {
            var target = document.getElementById(targetId);
            var link = document.querySelector('a[onclick="toggleCollapse(\'' + targetId + '\')"]');
            
            if (target.style.display === 'none' || target.style.display === '') {
                target.style.display = 'block';
                link.setAttribute('aria-expanded', 'true');
            } else {
                target.style.display = 'none';
                link.setAttribute('aria-expanded', 'false');
            }
        }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Simple collapse styles */
        .panel-collapse {
            display: none; /* Hidden by default */
            background-color: #f8f8f8;
            border-radius: 0 0 4px 4px;
            padding: 10px 15px;
            margin: 0 0 12px 0;
        }

        html, body {
            width: 100%;
            font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
            font-size: 15px;
            line-height: 1.6;
            background: #fff;
            color: #333;
        }

        .container {
            width: 94%;
            max-width: 100%;
            margin: 0 auto;
            padding: 16px 0 24px 0;
        }

        h3 { /* Main page title */
            text-align: center;
            margin: 16px 0 32px 0; /* Increased bottom margin */
            font-weight: bold;
            font-size: 1.3em;
        }

        h4 { /* Section titles like "目录" */
            text-align: left;
            margin: 28px 0 16px 0; /* Increased top margin */
            font-weight: bold;
            font-size: 1.15em; /* Slightly larger */
        }

        h5 { /* Sub-section titles */
            text-align: left;
            margin: 24px 0 12px 0;
            font-weight: bold;
            font-size: 1.05em;
        }

        p {
            text-align: left;
            text-indent: 0;
            margin: 12px 0;
        }

        .shb-indent { /* Specific indented paragraphs */
            text-indent: 2em;
            margin: 12px 0;
        }

        a {
            color: #1890ff;
            text-decoration: none;
            word-break: break-all;
        }
        a:hover, a:active {
            color: #0050b3;
            text-decoration: underline;
        }
        
        /* Remove click effects for collapsible links */
        .phone-settings-list a {
            -webkit-tap-highlight-color: transparent !important;
            -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            outline: none !important;
            background: transparent !important;
        }
        .phone-settings-list a:focus {
            outline: none !important;
            box-shadow: none !important;
            background: transparent !important;
            background-color: transparent !important;
        }
        .phone-settings-list a:active {
            color: #1890ff !important;
            text-decoration: none !important;
            background: transparent !important;
            background-color: transparent !important;
            outline: none !important;
            box-shadow: none !important;
        }
        .phone-settings-list a:hover {
            color: #1890ff !important;
            text-decoration: none !important;
            background: transparent !important;
            background-color: transparent !important;
        }
        
        /* Force color consistency in all states */
        .phone-settings-list a[aria-expanded="true"],
        .phone-settings-list a[aria-expanded="false"],
        .phone-settings-list a:visited,
        .phone-settings-list a:link {
            color: #1890ff !important;
            text-decoration: none !important;
            background: transparent !important;
            background-color: transparent !important;
        }
        
        /* Additional rules to prevent any highlight effects */
        .phone-settings-list a,
        .phone-settings-list a * {
            -webkit-tap-highlight-color: transparent !important;
            -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
        }

        .article {
            margin-bottom: 24px;
        }

        /* Table of Contents Styling */
        .toc-list {
            list-style: none;
            padding-left: 0;
            margin-bottom: 24px;
        }
        .toc-list > li {
            margin: 10px 0;
        }
        .toc-list .toc-sub-list { /* Nested dl for sub-items */
            list-style: none;
            padding-left: 20px; /* Indent sub-items */
            margin: 8px 0 8px 0;
        }
        .toc-list .toc-sub-list dt {
            font-weight: normal;
            margin: 6px 0;
        }
        
        /* General list styling for content sections */
        .article ul {
            list-style: none;
            padding-left: 20px; /* Indent list items */
            margin: 12px 0;
        }
        .article ul li {
            margin: 8px 0;
            position: relative; /* For custom bullet */
            padding-left: 15px; /* Space for custom bullet */
        }
        .article ul li::before {
            content: "•"; /* Custom bullet */
            position: absolute;
            left: 0;
            color: #1890ff; /* Bullet color */
        }

        /* Styling for definition lists (dl, dt, dd) in main content */
        .article dl {
            margin: 16px 0;
        }
        .article dt {
            font-weight: bold;
            margin: 12px 0 8px 0;
        }
        .article dd {
            margin: 8px 0 8px 20px; /* Indent definition details */
        }

        /* Bootstrap Collapse Panel Styling for Phone Settings */
        .phone-settings-list > dt { /* This is the clickable phone brand header */
            margin: 12px 0 0 0; /* Remove bottom margin if collapse directly follows */
        }
        .phone-settings-list > dt > div > div:first-child a { /* The link itself */
            font-weight: bold;
            font-size: 1.0em; /* Adjust as needed */
            display: block; /* Make it block for better click area */
            padding: 8px 0;
        }

        .panel-collapse p.shb-indent {
            margin: 10px 0;
        }
        .panel-collapse dl {
            margin: 10px 0;
            padding-left: 0; /* Reset padding for dl inside panel */
        }
        .panel-collapse dt {
            font-weight: bold;
            font-size: 1em;
            margin: 10px 0 5px 0;
            color: #333;
        }
        .panel-collapse dd {
            font-size: 0.95em;
            line-height: 1.5;
            margin: 5px 0 10px 20px;
            color: #555;
        }

    </style>
</head>

<body>
<div class="content">
    <div class="container">
        <h4>目录</h4>
        <ul class="toc-list">
            <li>一 <a href="#id-01">连接帮助</a></li>
            <li>二 <a href="#id-02">防止自动断开连接</a>
                <dl class="toc-sub-list">
                    <dt>1. <a href="#id-1">防止睿连智行被系统清理</a></dt>
                    <dt>2. <a href="#id-2">授予睿连智行相关权限</a></dt>
                    <dt>3. <a href="#id-3">常见机型相关选项示例</a></dt>
                </dl>
            </li>
        </ul>

        <!-- Connection Methods -->
        <div class="article">
            <h4 id="id-01">一 连接帮助</h4>
            <p class="shb-indent">
                打开睿连智行后，手机靠近支援睿连智行功能的仪表，按照手机提示，进行连接
            </p>
            <p class="shb-indent">无法自动连接时，请参考如下方式重试</p>
            <ul>
                <li>尝试手动连接</li>
                <li>重启设备并在手机最近任务中清除睿连智行任务</li>
                <li>尝试重新开关 wifi 和蓝牙</li>
            </ul>
        </div>

        <div class="article">
            <h4 id="id-02">二 防止自动断开连接措施</h4>
            <p class="shb-indent">
                为保障使用者驾驶期间的使用安全，睿连智行应用需要在后台持续运行，如果因为某些原因被手机自动清理，就会造成与仪表断的连接，从而影响驾驶者的使用，一下罗列了相关预防措施，供使用者参考。
            </p>
            
            <h5 id="id-1">1. 防止睿连智行被系统清理</h5>
            <dl>
                <dt id="id-1.1">1.1 防止电池管理误关闭</dt>
                <dd class="shb-indent">
                    手机锁屏或睿连智行在后台运行时，手机处于省电考量，会将睿连智行冻结，需要使用者手动讲睿连智行加入电池优化白名单
                    操作方法：电池相关设置中，请找到【电池】或【省电优化】相关入口，【关闭】睿连智行对应的省电优化选项。
                </dd>
                <dt id="id-1.2">1.2 防止应用被误清除</dt>
                <dd class="shb-indent">
                    手机处于节省内存等目的，会将后台的应用做自动清理，从而误清除睿连智行。
                    操作方法：点击底部的【多任务】按钮（或从屏幕底部上滑停留），进入多任务界面，在页面顶部选择【锁定】（或者点击锁定的图标）。
                </dd>
            </dl>
            
            <h5 id="id-2">2. 授予睿连智行相关权限</h5>
            <dl>
                <dt id="id-2.1">2.1 允许睿连智行自启动</dt>
                <dd class="shb-indent">
                    为防止手机锁屏或睿连智行在后台时，应用被系统自动关闭服务，建议用户开启【后台自启动】。
                    操作方法：在【手机管家】或【安全中心】的权限隐私管理中，或在【设置】入口下的【应用管理】中，请在这些入口中的【自启动管理】中开启睿连智行的自启动功能。
                </dd>
                <dt id="id-2.2">2.2 允许导航后台运行</dt>
                <dd class="shb-indent">
                    睿连智行后台运行期间，导航功能使用最为普遍，建议用户将位置权限设为【始终允许】，以防止导航无法定位。
                    操作方法：【设置】-【应用】-【权限管理】-【位置信息】，设置为【始终允许】。
                </dd>
                <dt id="id-2.3">2.3 允许消息通知</dt>
                <dd class="shb-indent">
                    为防止手机锁屏或睿连智行在后台时，被系统关闭进程，建议用户开启通知权限。
                    操作方法：【设置】-【通知管理】-找到睿连智行并打开【允许通知】选项。
                </dd>
            </dl>
            
            <h5 id="id-3">3. 常见机型相关选项示例</h5>
            <dl class="phone-settings-list">
                <dt>
                    <div>
                        <div>
                            3.1 <a href="javascript:void(0)" onclick="toggleCollapse('collapseOne')" aria-expanded="false">华为</a>
                        </div>
                        <div id="collapseOne" class="panel-collapse">
                            <p class="shb-indent">
                                华为权限设置教程
                                请按照以下步骤在设备系统中进行相关权限设置，以利 睿连智行 在后台运行，确保定位与通知机制等功能正常运作。
                            </p>
                            <dl>
                                <dt>步驟一</dt>
                                <dd>「设置」>「电池」> 启用「性能模式」，并关闭「省电模式」和「超级省电」</dd>
                                <dt>步骤二</dt>
                                <dd>「设置」>「电池」」>「应用启动管理」>「睿连智行」> 将「自动」关闭，并手动开启「自启动」、「关联启动」、「后台活动」</dd>
                                <dt>步骤三</dt>
                                <dd>「设置」> 搜寻「电池优化」>「所有应用」>「睿连智行」> 选择「不允许」</dd>
                                <dt>NOTE</dt>
                                <dd>权限为「电池优化」时，要将 睿连智行 设为「不允许」；反之，若权限为「忽略电池优化」，则要选择「允许」。</dd>
                                <dt>步骤四</dt>
                                <dd>「设置」>「安全」>「更多安全设置」>「使用情况访问权限」>「睿连智行」> 选择「允许访问使用记录」</dd>
                                <dt>步驟五</dt>
                                <dd>当 睿连智行 出现应用耗电提示时，选取「不再提示」，不要将应用直接关闭</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.2 <a href="javascript:void(0)" onclick="toggleCollapse('collapseTwo')" aria-expanded="false">小米</a>
                        </div>
                        <div id="collapseTwo" class="panel-collapse">
                            <p class="shb-indent">
                                小米权限设置教程 请按照以下步骤在设备系统中进行相关权限设置，以利 睿连智行 在后台运行，确保定位与通知机制等功能正常运作。
                            </p>
                            <dl>
                                <dt>步驟一</dt>
                                <dd>「设置」>「电量和性能」>「省电优化」> 关闭「省电模式」</dd>
                                <dt>步骤二</dt>
                                <dd>「设置」>「电量和性能」>「应用配置」>「睿连智行」> 选择「无限制」</dd>
                                <dt>步骤三</dt>
                                <dd>「手机管家」>「应用管理」>「睿连智行」> 开启「自启动」</dd>
                                <dt>步骤四</dt>
                                <dd>开启多任务处理界面（后台运行的软件）> 下拉 睿连智行 或长按 睿连智行 后按下锁头图示</dd>
                                <dt>NOTE</dt>
                                <dd>启用锁定后，点击多工界面的「X」不会关闭 睿连智行，但您仍须避免手动单独关软件。</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.3 <a href="javascript:void(0)" onclick="toggleCollapse('collapseThree')" aria-expanded="false">魅族</a>
                        </div>
                        <div id="collapseThree" class="panel-collapse">
                           <p class="shb-indent">
                                魅族手权限设置教程
                                请按照以下步骤在设备系统中进行相关权限设置，以利 睿连智行 在后台运行，确保定位与通知机制等功能正常运作。
                            </p>
                            <dl>
                                <dt>步驟一</dt>
                                <dd>「设置」>「应用管理」> 选择「正在运行」>「Android Services Library」>「设置」>「特殊访问权限」>「电池优化」>「所有应用」>「睿连智行」> 选择「不优化」</dd>
                                <dt>步骤二</dt>
                                <dd>「设置」>「应用管理」> 选择「正在运行」>「Android Services Library」>「设置」>「特殊访问权限」>「使用纪录访问权限」>「睿连智行」> 选择「允许」</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.4 <a href="javascript:void(0)" onclick="toggleCollapse('collapseFour')" aria-expanded="false">OPPO</a>
                        </div>
                        <div id="collapseFour" class="panel-collapse">
                            <p class="shb-indent">
                                Oppo 权限设置教程
                                请按照以下步骤在设备系统中进行相关权限设置，以利 睿连智行 在后台运行，确保定位与通知机制等功能正常运作
                            </p>
                            <dl>
                                <dt>步驟一</dt>
                                <dd>「设置」>「电池」>「省电模式」> 关闭「省电模式」、「设定电量自动开启」</dd>
                                <dt>步骤二</dt>
                                <dd>「设置」>「电池」>「应用耗电管理」>「睿连智行」> 开启所有选项：「允许唤醒前台」、「允许完全后台行为」、「允许应用自启动」、「允许应用关联启动」</dd>
                                <dt>步骤三</dt>
                                <dd>「设置」>「电池」>「智能省电场景」> 关闭「睡眠待机优化」</dd>
                                <dt>步骤四</dt>
                                <dd>「设置」>「电池」> 开启「高性能模式」</dd>
                                <dt>步驟五</dt>
                                <dd>「设置」>「应用管理」>「悬浮窗管理」> 开启 睿连智行</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.5 <a href="javascript:void(0)" onclick="toggleCollapse('collapseFive')" aria-expanded="false">Vivo</a>
                        </div>
                        <div id="collapseFive" class="panel-collapse">
                            <p class="shb-indent">
                                Vivo 权限设置教程
                                请按照以下步骤在设备系统中进行相关权限设置，以利 睿连智行 在后台运行，确保定位与通知机制等功能正常运作。
                            </p>
                            <dl>
                                <dt>步驟一</dt>
                                <dd>「设置」> 「电池」> 「后台耗电管理」>「睿连智行」> 选择「允许后台高耗电」</dd>
                                <dt>步骤二</dt>
                                <dd>「设置」> 「电池」> 关闭「省电模式」、「超级省电」</dd>
                                <dt>步骤三</dt>
                                <dd>「设置」>「安全与隐私」>「权限管理」>「睿连智行」>「单向权限设置」> 开启「自启动」、「锁屏显示」</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.6 <a href="javascript:void(0)" onclick="toggleCollapse('collapseSix')" aria-expanded="false">Samsung</a>
                        </div>
                        <div id="collapseSix" class="panel-collapse">
                            <p class="shb-indent">
                                三星权限设置教程
                                请按照以下步骤在设备系统中进行相关权限设置，以利 睿连智行 在后台执行，确保定位与通知机制等功能正常运作。
                            </p>
                            <dl>
                                <dt>步驟一</dt>
                                <dd>「设置」>「设备维护」>「电池」>「用电模式」> 选择「高性能」并关闭「自适应省电」</dd>
                                <dt>步骤二</dt>
                                <dd>「设置」>「设备维护」>「电池」>「应用程序耗电管理」> 关闭「自适应电池」、「让未使用的应用程序进入休眠」</dd>
                                <dt>步骤三</dt>
                                <dd>「设置」>「应用程序」>「睿连智行」>「电池」>「优化电池用量」选择「未优化」</dd>
                                <dt>步骤四</dt>
                                <dd>「设置」>「应用程序」> 右上角三个点点按钮 >「特殊访问」>「显示在顶部」、「通知访问」开启 睿连智行</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.7 <a href="javascript:void(0)" onclick="toggleCollapse('collapseSeven')" aria-expanded="false">Pixel</a>
                        </div>
                        <div id="collapseSeven" class="panel-collapse">
                            <p class="shb-indent">
                                Pixel 权限设置教程
                                请按照以下步骤在设备系统中进行相关权限设置，以利 睿连智行 在后台运行，确保定位与通知机制等功能正常运作。
                            </p>
                            <dl>
                                <dt>步驟一</dt>
                                <dd>「设置」>「电池」>「自适应电池」> 关闭「自适应电池」</dd>
                                <dt>步骤二</dt>
                                <dd>「设置」>「电池」>「省电模式」>「超级省电模式」>「何时使用」选择「永不使用」</dd>
                                <dt>步驟三</dt>
                                <dd>「设置」>「电池」>「省电模式」>「设置时间表」> 选择「没有时间表」</dd>
                                <dt>步骤四</dt>
                                <dd>「设置」>「电池」>「省电模式」> 点击「立即关闭」（若显示「立即开启」代表已关闭省电模式，毋需另外动作）</dd>
                                <dt>步驟五</dt>
                                <dd>「设置」>「应用和通知」>「应用信息」>「睿连智行」> 展开「高级」>「电池」> 关闭「后台限制」</dd>
                                <dt>步骤六</dt>
                                <dd>「设置」>「应用和通知」>「应用信息」>「睿连智行」> 展开「高级」>「电池」>「电池优化」> 选择「所有应用」>「睿连智行」>「不优化」</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.8 <a href="javascript:void(0)" onclick="toggleCollapse('collapseEight')" aria-expanded="false">Oneplus</a>
                        </div>
                        <div id="collapseEight" class="panel-collapse">
                            <p class="shb-indent">
                                Oneplus 权限设置教程
                                请按照以下步骤在设备系统中进行相关权限设置，以利 睿连智行 在后台运行，确保定位与通知机制等功能正常运作。
                            </p>
                            <dl>
                                <dt>步驟一</dt>
                                <dd>「设置」>「电池」>「省电模式」> 关闭「自动开启」，并点击「立即关闭」（若显示「立即开启」代表省电模式已关闭，毋需另外动作）</dd>
                                <dt>步骤二</dt>
                                <dd>「设置」>「电池」>「电池优化」> 点击「未优化应用」并改选「所有应用」>「睿连智行」> 选择「不优化」</dd>
                                <dt>步骤三</dt>
                                <dd>「设置」>「电池」>「智能电池管理」> 关闭「使用智能电池管理」</dd>
                                <dt>步骤四</dt>
                                <dd>「设置」>「应用和通知」>「查看全部＿个应用」>「睿连智行」>「流量使用情况」> 开启「后台数据」及「不限制数据流量用量」</dd>
                                <dt>步驟五</dt>
                                <dd>开启多任务处理介面（最近使用的程式）> 点击 睿连智行 右上角三个点点 > 按下「锁定」（若显示「解锁」代表已锁定，毋需另外动作）</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
            </dl>
        </div>
    </div>
</div>
</body>
</html>