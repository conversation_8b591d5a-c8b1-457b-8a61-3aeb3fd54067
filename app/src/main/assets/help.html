<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>RiderLink Help</title>
    <script>
        function toggleCollapse(targetId) {
            var target = document.getElementById(targetId);
            var link = document.querySelector('a[onclick="toggleCollapse(\'' + targetId + '\')"]');
            
            if (target.style.display === 'none' || target.style.display === '') {
                target.style.display = 'block';
                link.setAttribute('aria-expanded', 'true');
            } else {
                target.style.display = 'none';
                link.setAttribute('aria-expanded', 'false');
            }
        }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Simple collapse styles */
        .panel-collapse {
            display: none; /* Hidden by default */
            background-color: #f8f8f8;
            border-radius: 0 0 4px 4px;
            padding: 10px 15px;
            margin: 0 0 12px 0;
        }

        html, body {
            width: 100%;
            font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
            font-size: 15px;
            line-height: 1.6;
            background: #fff;
            color: #333;
        }

        .container {
            width: 94%;
            max-width: 100%;
            margin: 0 auto;
            padding: 16px 0 24px 0;
        }

        h3 { /* Main page title */
            text-align: center;
            margin: 16px 0 32px 0; /* Increased bottom margin */
            font-weight: bold;
            font-size: 1.3em;
        }

        h4 { /* Section titles like "Table of contents" */
            text-align: left;
            margin: 28px 0 16px 0; /* Increased top margin */
            font-weight: bold;
            font-size: 1.15em; /* Slightly larger */
        }

        h5 { /* Sub-section titles */
            text-align: left;
            margin: 24px 0 12px 0;
            font-weight: bold;
            font-size: 1.05em;
        }

        p {
            text-align: left;
            text-indent: 0;
            margin: 12px 0;
        }

        .shb-indent { /* Specific indented paragraphs */
            text-indent: 2em;
            margin: 12px 0;
        }

        a {
            color: #1890ff;
            text-decoration: none;
            word-break: break-all;
        }
        a:hover, a:active {
            color: #0050b3;
            text-decoration: underline;
        }
        
        /* Remove click effects for collapsible links */
        .phone-settings-list a {
            -webkit-tap-highlight-color: transparent !important;
            -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            outline: none !important;
            background: transparent !important;
        }
        .phone-settings-list a:focus {
            outline: none !important;
            box-shadow: none !important;
            background: transparent !important;
            background-color: transparent !important;
        }
        .phone-settings-list a:active {
            color: #1890ff !important;
            text-decoration: none !important;
            background: transparent !important;
            background-color: transparent !important;
            outline: none !important;
            box-shadow: none !important;
        }
        .phone-settings-list a:hover {
            color: #1890ff !important;
            text-decoration: none !important;
            background: transparent !important;
            background-color: transparent !important;
        }
        
        /* Force color consistency in all states */
        .phone-settings-list a[aria-expanded="true"],
        .phone-settings-list a[aria-expanded="false"],
        .phone-settings-list a:visited,
        .phone-settings-list a:link {
            color: #1890ff !important;
            text-decoration: none !important;
            background: transparent !important;
            background-color: transparent !important;
        }
        
        /* Additional rules to prevent any highlight effects */
        .phone-settings-list a,
        .phone-settings-list a * {
            -webkit-tap-highlight-color: transparent !important;
            -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
        }

        .article {
            margin-bottom: 24px;
        }

        /* Table of Contents Styling */
        .toc-list {
            list-style: none;
            padding-left: 0;
            margin-bottom: 24px;
        }
        .toc-list > li {
            margin: 10px 0;
        }
        .toc-list .toc-sub-list { /* Nested dl for sub-items */
            list-style: none;
            padding-left: 20px; /* Indent sub-items */
            margin: 8px 0 8px 0;
        }
        .toc-list .toc-sub-list dt {
            font-weight: normal;
            margin: 6px 0;
        }
        
        /* General list styling for content sections */
        .article ul {
            list-style: none;
            padding-left: 20px; /* Indent list items */
            margin: 12px 0;
        }
        .article ul li {
            margin: 8px 0;
            position: relative; /* For custom bullet */
            padding-left: 15px; /* Space for custom bullet */
        }
        .article ul li::before {
            content: "•"; /* Custom bullet */
            position: absolute;
            left: 0;
            color: #1890ff; /* Bullet color */
        }

        /* Styling for definition lists (dl, dt, dd) in main content */
        .article dl {
            margin: 16px 0;
        }
        .article dt {
            font-weight: bold;
            margin: 12px 0 8px 0;
        }
        .article dd {
            margin: 8px 0 8px 20px; /* Indent definition details */
        }

        /* Bootstrap Collapse Panel Styling for Phone Settings */
        .phone-settings-list > dt { /* This is the clickable phone brand header */
            margin: 12px 0 0 0; /* Remove bottom margin if collapse directly follows */
        }
        .phone-settings-list > dt > div > div:first-child a { /* The link itself */
            font-weight: bold;
            font-size: 1.0em; /* Adjust as needed */
            display: block; /* Make it block for better click area */
            padding: 8px 0;
        }

        .panel-collapse p.shb-indent {
            margin: 10px 0;
        }
        .panel-collapse dl {
            margin: 10px 0;
            padding-left: 0; /* Reset padding for dl inside panel */
        }
        .panel-collapse dt {
            font-weight: bold;
            font-size: 1em;
            margin: 10px 0 5px 0;
            color: #333;
        }
        .panel-collapse dd {
            font-size: 0.95em;
            line-height: 1.5;
            margin: 5px 0 10px 20px;
            color: #555;
        }

    </style>
</head>

<body>
<div class="content">
    <div class="container">
        <h4>Table of contents</h4>
        <ul class="toc-list">
            <li>一 <a href="#id-01">Connection help</a></li>
            <li>二 <a href="#id-02">Prevent automatic disconnection</a>
                <dl class="toc-sub-list">
                    <dt>1. <a href="#id-1">Prevent RiderLink be cleaned up by the system</a></dt>
                    <dt>2. <a href="#id-2">Grant relevant permissions to RiderLink</a></dt>
                    <dt>3. <a href="#id-3">Examples of setting options for common mobile phones</a></dt>
                </dl>
            </li>
        </ul>

        <!-- Connection Methods -->
        <div class="article">
            <h4 id="id-01">一 Connection help</h4>
            <p class="shb-indent">
                After opening the App, bring your phone close to the device that supports RiderLink interconnection function, and follow the phone prompts to connect;
            </p>
            <p class="shb-indent">If automatic connection failed, please refer to the following methods to try again.</p>
            <ul>
                <li>Try connecting manually</li>
                <li>Restart the device and clear the RiderLink task in recent tasks on the phone</li>
                <li>Try disable/enable WLAN and Bluetooth again</li>
            </ul>
        </div>

        <div class="article">
            <h4 id="id-02">二 Prevent Automatic Disconnection</h4>
            <p class="shb-indent">
                In order to ensure the user's safety while driving, the RiderLink App needs to keep active in the background. If it is automatically cleared by the mobile phone for some reason, it will cause a disconnection from the device. Here is a list of relevant Precautionary measures for reference.
            </p>
            
            <h5 id="id-1">1. Prevent RiderLink be cleaned up by the system</h5>
            <dl>
                <dt id="id-1.1">1.1 Prevent RiderLink from being closed by battery management</dt>
                <dd class="shb-indent">
                    When the phone is locked or RiderLink is running in the background, the phone is considering power saving and will freeze RiderLink. Users need to manually add RiderLink to the battery optimization whitelist.
                    Operation method: In the battery-related settings, please find [Battery] or the relevant entrance of [Power Saving Optimization], [Close] the power saving optimization option corresponding to RiderLink.
                </dd>
                <dt id="id-1.2">1.2 Prevent RiderLink from being automatically cleared by the system</dt>
                <dd class="shb-indent">
                    For the purpose of saving memory, the mobile phone will automatically clean up the background Apps, thus clearing RiderLink by mistake.
                    Operation method: Click the [Multi-tasking] button at the bottom (or swipe up from the bottom of the screen to hold) to enter the multi-tasking interface, select [Lock] at the top of the page (or click the locked icon).
                </dd>
            </dl>
            
            <h5 id="id-2">2. Grant relevant permissions to RiderLink</h5>
            <dl>
                <dt id="id-2.1">2.1 Allow RiderLink to start automatically</dt>
                <dd class="shb-indent">
                    In order to prevent the App from automatically shut down by the system when the phone is locked or RiderLink is in the background, it is recommended that users enable [Background Auto-Start].
                    Operation method: In the permissions and privacy management of [Mobile Manager] or [Security Center], or in [Application Management] under the [Settings] page, please enable the RiderLink's [Auto-Start Management].
                </dd>
                <dt id="id-2.2">2.2 Allow navigation to run in the background</dt>
                <dd class="shb-indent">
                    When RiderLink is running in the background, the navigation function is most commonly used. It is recommended that users set the location permission to [Always Allow] to prevent navigation from being unable to locate.
                    Operation method: [Settings]-[Application]-[Permission Management]-[Location Information], set Riderlink's option to [Allow all the time].
                </dd>
                <dt id="id-2.3">2.3 Allow message notification</dt>
                <dd class="shb-indent">
                    To prevent the RiderLink's process be shuted down when the phone is locked or RiderLink is in the background, it is recommended that users enable notification permissions.
                    Operation method: [Settings]-[Notification Management]-Find RiderLink and enable the [Allow Notifications] option.
                </dd>
            </dl>
            
            <h5 id="id-3">3. Examples of setting options for common mobile phones</h5>
            <dl class="phone-settings-list">
                <dt>
                    <div>
                        <div>
                            3.1 <a href="javascript:void(0)" onclick="toggleCollapse('collapseOne')" aria-expanded="false">Huawei</a>
                        </div>
                        <div id="collapseOne" class="panel-collapse">
                            <p class="shb-indent">
                                Please follow the steps below and set the required permissions in your device settings, so that RiderLink can stay running in the background and ensure the proper functioning of locations and notifications.
                            </p>
                            <dl>
                                <dt>Step 1</dt>
                                <dd>"Settings" > "Battery" > enable "Performance mode" and disable "Power saving mode" and "Ultra power saving mode"</dd>
                                <dt>Step 2</dt>
                                <dd>"Settings" > "Battery" > "App launch" > "RiderLink" > turn off "Manage automatically" and turn on "Auto-launch", "Secondary launch" and "Run in background"</dd>
                                <dt>Step 3</dt>
                                <dd>"Settings" > search for "Battery optimization" > "All apps" > "RiderLink" > select "Don't allow"</dd>
                                <dt>NOTE</dt>
                                <dd>Select "Don't allow" for "Battery optimization" and "Allow" for "Ignore battery optimization".</dd>
                                <dt>Step 4</dt>
                                <dd>"Settings" > search for "Special access" > "Usage information access" > "RiderLink" > select "Allow usage access"</dd>
                                <dt>Step 5</dt>
                                <dd>When the power consumption alert comes up, tap 'Ignore' instead of closing RiderLink.</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.2 <a href="javascript:void(0)" onclick="toggleCollapse('collapseTwo')" aria-expanded="false">Xiaomi</a>
                        </div>
                        <div id="collapseTwo" class="panel-collapse">
                            <p class="shb-indent">
                                Please follow the steps below and set the required permissions in your device settings, so that RiderLink can stay running in the background and ensure the proper functioning of locations and notifications.
                            </p>
                            <dl>
                                <dt>Step 1</dt>
                                <dd>"Settings" > "Battery performance" > "Power" > disable "Battery saver"</dd>
                                <dt>Step 2</dt>
                                <dd>"Settings" > "Battery performance" > "Choose apps" > "RiderLink" > "No restrictions"</dd>
                                <dt>Step 3</dt>
                                <dd>"Security" > "Manage apps" > "RiderLink" > enable "Auto start"</dd>
                                <dt>Step 4</dt>
                                <dd>App overview (recent apps) > drag RiderLink downwards, or tap and hold on it and hit on the lock icon</dd>
                                <dt>NOTE</dt>
                                <dd>Once the locking is done, pressing the 'X' on your recent list won't clear RiderLink, but you still have to avoid manually closing it as an individual app.</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.3 <a href="javascript:void(0)" onclick="toggleCollapse('collapseThree')" aria-expanded="false">Meizu</a>
                        </div>
                        <div id="collapseThree" class="panel-collapse">
                           <p class="shb-indent">
                                Please follow the steps below and set the required permissions in your device settings, so that RiderLink can stay running in the background and ensure the proper functioning of locations and notifications.
                            </p>
                            <dl>
                                <dt>Step 1</dt>
                                <dd>"Settings" > "Application Management" > Select "Running" > "Android Services Library" > "Settings" > "Special Access Rights" > "Battery Optimization" > "All Apps" > "RiderLink" > Select "No optimization"</dd>
                                <dt>Step 2</dt>
                                <dd>"Settings" > "Application Management" > Select "Running" > "Android Services Library" > "Settings" > "Special Access Permissions" > "Usage History Access Permissions" > "RiderLink" > Select "Allow"</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.4 <a href="javascript:void(0)" onclick="toggleCollapse('collapseFour')" aria-expanded="false">OPPO</a>
                        </div>
                        <div id="collapseFour" class="panel-collapse">
                            <p class="shb-indent">
                                Please follow the steps below and set the required permissions in your device settings, so that RiderLink can stay running in the background and ensure the proper functioning of locations and notifications.
                            </p>
                            <dl>
                                <dt>Step 1</dt>
                                <dd>"Settings" > "Battery" > "Power saving mode" > disable "Power saving mode" and "Turn on at specified battery level"</dd>
                                <dt>Step 2</dt>
                                <dd>"Settings" > "Battery" > "App battery management" > "RiderLink" > enable all permissions: "Allow foreground activity", "Allow background activity", "Allow auto launch" and "Allow related apps or services to be launched"</dd>
                                <dt>Step 3</dt>
                                <dd>"Settings" > "Battery" > "Power saver options" > turn off "Sleep standby optimization"</dd>
                                <dt>Step 4</dt>
                                <dd>"Settings" > "Battery" > turn on "High performance mode"</dd>
                                <dt>Step 5</dt>
                                <dd>"Settings" > "App management" > "Display over other apps" > enable RiderLink</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.5 <a href="javascript:void(0)" onclick="toggleCollapse('collapseFive')" aria-expanded="false">Vivo</a>
                        </div>
                        <div id="collapseFive" class="panel-collapse">
                            <p class="shb-indent">
                                Please follow the steps below and set the required permissions in your device settings, so that RiderLink can stay running in the background and ensure the proper functioning of locations and notifications.
                            </p>
                            <dl>
                                <dt>Step 1</dt>
                                <dd>"Settings" > search for "High background battery usage" > "RiderLink" > select "High background power usage"</dd>
                                <dt>Step 2</dt>
                                <dd>"Settings" > "Battery" > disable "Battery saver" and "Super power-saving mode"</dd>
                                <dt>Step 3</dt>
                                <dd>"Settings" > "Security & privacy" > "Permission management" > "RiderLink" "Single permission settings" > enable "Auto start" and "Display on lock screen"</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.6 <a href="javascript:void(0)" onclick="toggleCollapse('collapseSix')" aria-expanded="false">Samsung</a>
                        </div>
                        <div id="collapseSix" class="panel-collapse">
                            <p class="shb-indent">
                                Please follow the steps below and set the required permissions in your device settings, so that RiderLink can stay running in the background and ensure the proper functioning of locations and notifications.
                            </p>
                            <dl>
                                <dt>Step 1</dt>
                                <dd>"Settings" > "Device care" > "Battery" > "Power mode" > select "High performance" and turn off "Adaptive power saving"</dd>
                                <dt>Step 2</dt>
                                <dd>"Settings" > "Device care" > "Battery" > "App power management" > disable "Adaptive battery" and "Put unused apps to sleep"</dd>
                                <dt>Step 3</dt>
                                <dd>"Settings" > "Apps" > "RiderLink" > "Battery" > "Optimize battery usage" > choose "Not optimized"</dd>
                                <dt>Step 4</dt>
                                <dd>"Settings" > "Apps" > three dots on the top-right > "Special access" > go to "Appear on top" and "Notification access" and enable RiderLink</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.7 <a href="javascript:void(0)" onclick="toggleCollapse('collapseSeven')" aria-expanded="false">Pixel</a>
                        </div>
                        <div id="collapseSeven" class="panel-collapse">
                            <p class="shb-indent">
                                Please follow the steps below and set the required permissions in your device settings, so that RiderLink can stay running in the background and ensure the proper functioning of locations and notifications.
                            </p>
                            <dl>
                                <dt>Step 1</dt>
                                <dd>"Settings" > "Battery" > "Adaptive battery" > turn off "Adaptive battery"</dd>
                                <dt>Step 2</dt>
                                <dd>"Settings" > "Battery" > "Battery saver" > "Extreme battery saver" > select "Never use" for "When to use"</dd>
                                <dt>Step 3</dt>
                                <dd>"Settings" > "Battery" > "Battery saver" > "Set a schedule" > choose "No schedule"</dd>
                                <dt>Step 4</dt>
                                <dd>"Settings" > "Battery" > "Battery saver" > tap "Turn off now" (If it says "Turn on now", that means battery saver is disabled. No further actions required."</dd>
                                <dt>Step 5</dt>
                                <dd>"Settings" > "Apps and notifications" > "App info" > "RiderLink" > open "Advanced" > "Battery" > turn off "Background restriction"</dd>
                                <dt>Step 6</dt>
                                <dd>"Settings" > "Apps and notifications" > "App info" > "RiderLink" > open "Advanced" > "Battery" > "Battery optimization" > select "All apps" > "RiderLink" > "Don't optimize"</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
                <dt>
                    <div>
                        <div>
                            3.8 <a href="javascript:void(0)" onclick="toggleCollapse('collapseEight')" aria-expanded="false">Oneplus</a>
                        </div>
                        <div id="collapseEight" class="panel-collapse">
                            <p class="shb-indent">
                                Please follow the steps below and set the required permissions in your device settings, so that RiderLink can stay running in the background and ensure the proper functioning of locations and notifications.
                            </p>
                            <dl>
                                <dt>Step 1</dt>
                                <dd>"Settings" > "Battery" > "Battery saver" > disable "Turn on automatically" and tap "Turn off now" (If it reads "Turn on now", that means battery saver has been switched off. No further actions required.)</dd>
                                <dt>Step 2</dt>
                                <dd>"Settings" > "Battery" > "Battery optimization" > hit "Not optimized apps" and select "All apps" > "RiderLink" > choose "Don't optimize"</dd>
                                <dt>Step 3</dt>
                                <dd>"Settings" > "Battery" > "Adaptive battery" > disable "Use adaptive battery"</dd>
                                <dt>Step 4</dt>
                                <dd>"Settings" > "Apps & notifications" > "See all __ apps" > "RiderLink" > "Data usage" > allow "Background data" and "Unrestricted data usage"</dd>
                                <dt>Step 5</dt>
                                <dd>Open App Overview (recent apps) > tap the three dots on the top-right corner of the RiderLink window > click "Lock" (If it reads "Unlock", that means the app has been locked. No further actions required.)</dd>
                            </dl>
                        </div>
                    </div>
                </dt>
            </dl>
        </div>
    </div>
</div>
</body>
</html>