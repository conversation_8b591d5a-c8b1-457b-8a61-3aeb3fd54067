<resources xmlns:tools="http://schemas.android.com/tools">



    <style name="PresentationDialog" parent="@android:style/Theme.Material.NoActionBar.Fullscreen" />

    <style name="Preference.Material" tools:override="true">
        <item name="android:layout" tools:ignore="PrivateResource">@layout/preference_material</item>
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
        <item name="singleLineTitle">false</item>
        <item name="iconSpaceReserved">false</item>
    </style>

    <style name="POLICY_DIALOG" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!-- 无边框 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 没有标题 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 背景是否半透明 -->
    </style>

    <style name="BottomDialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="SearchDialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:navigationBarColor">
            @android:color/transparent
        </item>
        <item name="android:statusBarColor">
            @android:color/transparent
        </item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="BottomDialog.Animation" parent="Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/translate_dialog_in</item>
        <item name="android:windowExitAnimation">@anim/translate_dialog_out</item>
    </style>

    <style name="Switch">
        <!--track是switch轨迹部分,这里我们用了一个selector来解决不同状态下轨迹的颜色-->
        <item name="android:track">@drawable/switch_track</item>
        <!--thumb是switch滑块,这里我们用了Widget.AppCompat.CompoundButton.Switch下带的资源,如果有需要,请自行更换图标-->
        <item name="android:thumb">@drawable/switch_thumb</item>
        <item name="android:background">@null</item>
        <!--switch上的文字,插入占位符,解决部分手机不能显示问题-->
        <item name="android:textOn"> </item>
        <item name="android:textOff"> </item>
        <!--这个东西才能控制整个开关的宽度, 有些人会发现设置width会无效,设置一下这个就好啦-->
        <item name="android:switchMinWidth">32dp</item>
    </style>

    <style name="ConnectDialog"  parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:navigationBarColor">
            @android:color/transparent
        </item>
        <item name="android:statusBarColor">
            @android:color/transparent
        </item>
    </style>

    <style name="Dialog_Fullscreen" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:navigationBarColor">
            @android:color/transparent
        </item>
        <item name="android:statusBarColor">
            @android:color/transparent
        </item>

    </style>
</resources>
