<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <string name="app_name">Rider</string>
    <string name="config_name">:sunmedia:Autolink</string>
    <string name="notification_sub_text">Tap to open Rider</string>
    <string name="notification_mirror">Rider running</string>
    <string name="bt_title">BT&amp;WiFi</string>
    <string name="gps_signal_week">Weak GPS signal</string>
    <string name="no_permission_message">Missing permissions</string>
    <string name="policy_title">Privacy Policy</string>
    <string name="policy_text">Welcome to Rider! We will use the 《Rider Privacy Policy》 to help you understand our collection, use, storage and sharing of personal information, as well as the relevant rights you enjoy. \n
           • In order to provide you with complete functional services, we need to use some of your storage permissions, access to device information and other permissions and information. \n
           • We will use industry-leading security technology to protect your personal information. \n
           You can learn more detailed personal information processing rules such as the correspondence between personal information types and uses by reading the full version of the user privacy policy. \n
           If you agree, please click agree to start accepting our service.</string>
    <string name="policy_refused_title">Thanks for using Rider</string>
    <string name="policy_refused_text">You need to agree to this privacy policy before you can continue to use Rider.
        You can view the full 《Rider Privacy Policy》 Agreement again.</string>
    <string name="rule_agree">Agree</string>
    <string name="select_agree">OK</string>
    <string name="rule_disagree">Disagree</string>
    <string name="select_disagree">Cancel</string>
    <string name="about">About Rider</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="rider_about_title">About Rider</string>
    <string name="sunmedia_technology_co_ltd">©2022 SunMedia Technology Co.,Ltd. 版权所有</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="help_title">RiderLink Help</string>
    <string name="settings_title">Settings</string>
    <string name="night_mode">Night mode</string>
    <string name="daynight_auto">Auto night mode</string>
    <string name="help_center">Help Center</string>
    <string name="clear_config">Clear Config</string>
    <string name="no_network_message">Network unavailable, please retry.</string>
    <string name="delete_config_title">Notice</string>
    <string name="delete_config_message">Delete %1$s?</string>
    <string name="delete_config_success">Deleted</string>
    <string name="connect_failed">Connection failed, try manual connect</string>
    <string name="wifi_connecting">BT connected, WiFi connecting, get closer</string>
    <string name="connected_append">Connected:</string>
    <string name="connecting">BT connecting, get closer</string>
    <string name="connect_ble_failed">BT disconnected, WiFi connected</string>
    <string name="connect_ble_wifi_failed">Disconnected, try manual connect</string>
    <string name="connect_idle_state">Not connected, get close to connect.</string>
    <string name="connect_idle_state_with_name">Connecting %1$s, get closer</string>
    <string name="connect_dialog_title">Connect your device</string>
    <string name="connect_dialog_content">Get close to connect.</string>
    <string name="connect_button_text">Later</string>
    <string name="connect_button_disconnect">End</string>
    <string name="connect_button_disconnect_cancel">Wait</string>
    <string name="connect_list_dialog_title">Manual</string>
    <string name="connect_list_dialog_content">Tap to search nearby devices.</string>
    <string name="search_button_title">Search</string>
    <string name="disconnect">End</string>
    <string name="mirror">Mirror</string>
    <string name="weather">Weather</string>
    <string name="connect_status">Status</string>
    <string name="home_map_text">Built-in nav, one-click start.</string>
    <string name="home_map_title">Navigation</string>
    <string name="home_dvr_title">View DVR</string>
    <string name="home_dvr_text">Record and replay wonderful moments.</string>
    <string name="home_music_title">Music</string>
    <string name="home_music_text">Listen while moving, double the joy.</string>
    <string name="home_connect_button_text">Manual</string>
    <string name="slide_up_text">Slide up</string>
    <string name="stop_mirror">Stop mirror</string>
    <string name="no_mediaprojection_permission">No screen record permission</string>
    <string name="humidity">℃·Humidity</string>
    <string name="altitude">·Altitude</string>
    <string name="pressure">m·Pressure</string>
    <string name="connected_dialog_content">Get close to connect.</string>
    <string name="connected_dialog_title">Not connected.</string>
    <string name="disconnected_dialog_title">Disconnect?</string>
    <string name="disconnected_dialog_content">Can reconnect on homepage</string>
    <string name="search_text">Searching, start device</string>
    <string name="unknown">Unknown</string>
    <string name="distance">Distance</string>
    <string name="connected">Connected</string>
    <string name="connecting_status">Connecting…</string>
    <string name="error_title">Error</string>
    <string name="error_message">Connect failed. Error:</string>
    <string name="pause">Paused</string>
    <string name="prepare">Prepare</string>
    <string name="all_dvr">All</string>
    <string name="dvr_no_support_with_wifi_direct">Turn on WiFi first</string>
    <string name="dvr_downloaded">Downloaded</string>
    <string name="dvr_loading">Loading</string>
    <string name="dvr">DVR Recorder</string>
    <string name="dvr_download_error">Download error</string>
    <string name="dvr_download_start">Added task %1$s</string>
    <string name="logcat">Logcat Details</string>
    <string name="share">Share</string>
    <string name="version">App Version</string>
    <string name="autolink_version">AutoLink Ver</string>
    <string name="debug">Debug</string>
    <string name="riderlink_version">Riderlink Ver</string>
    <string name="open_logcat">Open logcat</string>
    <string name="simulated_navigation">Sim navigation</string>
    <string name="cancel">Cancel</string>
    <string name="delete">Delete</string>
    <string name="location_failed_message">Location failed</string>
    
    <!-- Layout hardcoded text strings -->
    <string name="rescan">Rescan</string>
    <string name="back_to_home">Home</string>
    <string name="confirm_delete">Confirm Delete</string>
    <string name="delete_warning">Cannot recover after delete, confirm?</string>
    <string name="notice">Notice</string>
    <string name="device_name">Device</string>
    <string name="address">Address</string>
    <string name="distance_label">Distance</string>
    <string name="connection_status">Status</string>
    <string name="phone_number">Phone:</string>
    <string name="click_to_copy">Copy</string>
    <string name="exit_app">Exit</string>
    <string name="agree_and_continue">Agree</string>
    <string name="title_label">Title</string>
    <string name="text_content">Content</string>
    <string name="file_name">File</string>
    <string name="product_key">Product Key</string>
    <string name="uuid_label">UUID</string>
    <string name="crash_log">Crash Log</string>
    <string name="logcat_log">Logcat Log</string>
    
    <!-- Version info related -->
    <string name="app_version_info">App Version Info</string>
    <string name="autolink_version_info">AutoLink Version Info</string>
    <string name="riderlink_version_info">RiderLink Version Info</string>
    <string name="logcat_details">Logcat Details</string>
    <string name="detailed_info">Details</string>

    <string name="dialog_search_et_hint">Find an address or place</string>
    <string name="home_connect_scan_text">Scan</string>

    <string name="mirror_context">Mirror running</string>
</resources>
