<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/homeFragment">
    <fragment
        android:id="@+id/homeFragment"
        android:name="com.link.riderlink.features.home.HomeFragment"
        android:label="fragment_home"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_homeFragment_to_mapFragment"
            app:destination="@id/mapFragment" />
        <action
            android:id="@+id/action_homeFragment_to_settingFragment"
            app:destination="@id/settingFragment" />
        <action
            android:id="@+id/action_homeFragment_to_dvrFragment"
            app:destination="@id/dvrFragment" />
        <action
            android:id="@+id/action_homeFragment_to_routeFragment"
            app:destination="@id/routeFragment" />
        <action
            android:id="@+id/action_homeFragment_to_privacyFragment"
            app:destination="@id/privacyFragment" />
        <action
            android:id="@+id/action_homeFragment_to_helpFragment"
            app:destination="@id/helpFragment" />
        <action
            android:id="@+id/action_homeFragment_to_aboutFragment"
            app:destination="@id/aboutFragment" />
        <action
            android:id="@+id/action_homeFragment_to_agreementFragment"
            app:destination="@id/agreementFragment" />
    </fragment>
    <fragment
        android:id="@+id/settingFragment"
        android:name="com.link.riderlink.features.my.SettingFragment"
        android:label="fragment_setting"
        tools:layout="@layout/fragment_setting">
        <action
            android:id="@+id/action_settingFragment_to_privacyFragment"
            app:destination="@id/privacyFragment" />
        <action
            android:id="@+id/action_settingFragment_to_helpFragment"
            app:destination="@id/helpFragment" />
        <action
            android:id="@+id/action_settingFragment_to_aboutFragment"
            app:destination="@id/aboutFragment" />
        <action
            android:id="@+id/action_settingFragment_to_agreementFragment"
            app:destination="@id/agreementFragment" />
    </fragment>
    <fragment
        android:id="@+id/mapFragment"
        android:name="com.link.riderlink.features.map.MapFragment"
        android:label="MapFragment">
        <action
            android:id="@+id/action_mapFragment_to_routeFragment"
            app:destination="@id/routeFragment" />
    </fragment>
    <fragment
        android:id="@+id/dvrFragment"
        android:name="com.link.riderlink.features.dvr.DvrFragment"
        android:label="fragment_dvr"
        tools:layout="@layout/fragment_dvr" />
    <fragment
        android:id="@+id/routeFragment"
        android:name="com.link.riderlink.features.route.RouteFragment"
        android:label="RouteFragment"/>
    <fragment
        android:id="@+id/privacyFragment"
        android:name="com.link.riderlink.features.privacy.PrivacyFragment"
        android:label="fragment_privacy"
        tools:layout="@layout/fragment_privacy" />
    <fragment
        android:id="@+id/helpFragment"
        android:name="com.link.riderlink.features.help.HelpFragment"
        android:label="fragment_help"
        tools:layout="@layout/fragment_help" />
    <fragment
        android:id="@+id/aboutFragment"
        android:name="com.link.riderlink.features.about.AboutFragment"
        android:label="fragment_about"
        tools:layout="@layout/fragment_about">
        <action
            android:id="@+id/action_aboutFragment_to_hidedebugFragment"
            app:destination="@id/hidedebugFragment" />
    </fragment>
    <fragment
        android:id="@+id/agreementFragment"
        android:name="com.link.riderlink.features.agreement.AgreementFragment"
        android:label="fragment_agreement"
        tools:layout="@layout/fragment_agreement" />
    <fragment
        android:id="@+id/hidedebugFragment"
        android:name="com.link.riderlink.features.hide.HideDebugFragment"
        android:label="fragment_hidedebug"
        tools:layout="@layout/fragment_hidedebug">
        <action
            android:id="@+id/action_hidedebugFragment_to_appversionFragment"
            app:destination="@id/appversionFragment" />
        <action
            android:id="@+id/action_hidedebugFragment_to_riderlinkversionFragment"
            app:destination="@id/riderlinkversionFragment" />
        <action
            android:id="@+id/action_hidedebugFragment_to_autolinkversionFragment"
            app:destination="@id/autolinkversionFragment" />
        <action
            android:id="@+id/action_hidedebugFragment_to_productkeyFragment"
            app:destination="@id/productkeyFragment" />
        <action
            android:id="@+id/action_hidedebugFragment_to_uuidFragment"
            app:destination="@id/uuidFragment" />
        <action
            android:id="@+id/action_hidedebugFragment_to_logcatFragment"
            app:destination="@id/logcatFragment" />
    </fragment>
    <fragment
        android:id="@+id/appversionFragment"
        android:name="com.link.riderlink.features.hide.AppVersionFragment"
        android:label="fragment_agreement"
        tools:layout="@layout/fragment_agreement" />
    <fragment
        android:id="@+id/riderlinkversionFragment"
        android:name="com.link.riderlink.features.hide.RiderLinkVersionFragment"
        android:label="fragment_agreement"
        tools:layout="@layout/fragment_agreement" />
    <fragment
        android:id="@+id/autolinkversionFragment"
        android:name="com.link.riderlink.features.hide.AutoLinkVersionFragment"
        android:label="fragment_agreement"
        tools:layout="@layout/fragment_agreement" />
    <fragment
        android:id="@+id/productkeyFragment"
        android:name="com.link.riderlink.features.hide.ProductKeyFragment"
        android:label="fragment_agreement"
        tools:layout="@layout/fragment_agreement" />
    <fragment
        android:id="@+id/uuidFragment"
        android:name="com.link.riderlink.features.hide.UuidFragment"
        android:label="fragment_agreement"
        tools:layout="@layout/fragment_uuid" />
    <fragment
        android:id="@+id/logcatFragment"
        android:name="com.link.riderlink.features.hide.LogCatFragment"
        android:label="fragment_logcatment"
        tools:layout="@layout/fragment_logcat">
        <action
            android:id="@+id/action_logcatFragment_to_detailedFragment"
            app:destination="@id/detailedFragment" />
    </fragment>
    <fragment
        android:id="@+id/detailedFragment"
        android:name="com.link.riderlink.features.hide.logcat.DetailedFragment"
        android:label="fragment_detailedment"
        tools:layout="@layout/fragment_detailed" />
</navigation>