<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/main_connect_background"
            android:layout_width="90dp"
            android:layout_height="32dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <ImageView
            android:id="@+id/main_connect_bitmap"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginTop="9dp"
            android:layout_marginStart="12dp"
            app:layout_constraintTop_toTopOf="@+id/main_connect_background"
            app:layout_constraintLeft_toLeftOf="@+id/main_connect_background"/>
        <TextView
            android:id="@+id/main_connect_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:layout_marginStart="29dp"
            android:textSize="12sp"
            android:textColor="#FFFFFF"
            app:layout_constraintTop_toTopOf="@+id/main_connect_background"
            app:layout_constraintLeft_toLeftOf="@+id/main_connect_background"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>