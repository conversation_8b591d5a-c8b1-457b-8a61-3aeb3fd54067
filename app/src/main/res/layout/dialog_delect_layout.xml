<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_marginLeft="20dp"
    android:layout_marginRight="20dp"
    android:background="#00000000">

    <ImageView
        android:id="@+id/dialog_background"
        android:layout_width="match_parent"
        android:layout_height="193dp"
        android:background="@drawable/delete_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/ds_title"
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:layout_weight="1"
        android:text="@string/confirm_delete"
        android:gravity="center"
        android:textColor="#56595e"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="18sp" />


    <TextView
        android:id="@+id/ds_text"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="@string/delete_warning"
        android:gravity="center"
        android:textColor="#56595e"
        android:layout_marginTop="75dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="14sp" />
    <ImageButton
        android:id="@+id/select_cancel"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_gravity="center"
        android:background="@drawable/delete_btn_cancel"
        android:layout_marginBottom="30dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintRight_toLeftOf="@+id/select_ok"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageButton
        android:id="@+id/select_ok"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_gravity="center"
        android:background="@drawable/delete_btn_ok"
        android:layout_marginBottom="30dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintLeft_toRightOf="@+id/select_cancel"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>