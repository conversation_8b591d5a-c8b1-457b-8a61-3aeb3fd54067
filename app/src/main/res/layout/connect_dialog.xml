<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="20dp"
    android:layout_marginRight="20dp"
    android:background="#00000000">

    <View
        android:id="@+id/connect_dialog_background_img"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <TextView
        android:id="@+id/connect_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:layout_marginTop="30dp"
        android:text="@string/connect_dialog_title"
        android:textColor="#5C7BD7"
        android:textSize="18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <TextView
        android:id="@+id/connect_dialog_content"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:layout_marginTop="8dp"
        android:text="@string/connect_dialog_content"
        android:textColor="#8C909E"
        android:textSize="12sp"
        app:layout_constraintTop_toBottomOf="@+id/connect_dialog_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <ImageView
        android:id="@+id/connect_dialog_bitmap"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:src="@drawable/un_connect"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connect_dialog_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/button_dialog_select"
            android:alpha="0.1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:text="@string/connect_button_text"
            android:textSize="14sp"
            android:textColor="#5C7BD7"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connect_dialog_button_ok"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/connect_dialog_button_cancel">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_dialog_select"
            android:alpha="0.1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:text="@string/connect_button_disconnect"
            android:textSize="14sp"
            android:textColor="#5C7BD7"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connect_dialog_button_cancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/connect_dialog_button_ok">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_dialog_select"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:text="@string/connect_button_disconnect_cancel"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>