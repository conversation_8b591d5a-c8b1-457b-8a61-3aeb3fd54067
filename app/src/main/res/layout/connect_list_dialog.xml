<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/connect_dialog_list_background_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        android:background="@drawable/connect_list_background"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <TextView
        android:id="@+id/connect_list_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:layout_marginTop="20dp"
        android:text="@string/connect_list_dialog_title"
        android:textColor="@color/black_tx"
        android:textSize="18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <ImageView
        android:id="@+id/connect_list_dialog_bitmap"
        android:layout_width="match_parent"
        android:layout_height="260dp"
        android:layout_marginTop="74dp"
        android:src="@drawable/wifi_ble"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/connect_dialog_list"
        android:layout_width="match_parent"
        android:layout_height="316dp"
        android:layout_marginTop="62dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <ImageView
        android:id="@+id/connect_list_dialog_cancel"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginTop="21dp"
        android:layout_marginEnd="20dp"
        android:src="@drawable/cancel_1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <TextView
        android:id="@+id/connect_list_dialog_content"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:layout_marginTop="17dp"
        android:text="@string/connect_list_dialog_content"
        android:textColor="#8C909E"
        android:textSize="12sp"
        app:layout_constraintTop_toBottomOf="@+id/connect_list_dialog_bitmap"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connect_list_dialog_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="35dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">
        <ImageView
            android:id="@+id/img_dialog_button"
            android:layout_width="240dp"
            android:layout_height="wrap_content"
            android:background="@drawable/button_dialog_select"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
        <TextView
            android:id="@+id/tv_dialog_button"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:text="@string/search_button_title"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connect_list_dialog_button_re"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginBottom="35dp"
        android:visibility="gone"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/connect_list_dialog_button_cancel">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_dialog_select"
            android:alpha="0.1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:text="@string/rescan"
            android:textSize="14sp"
            android:textColor="#5C7BD7"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connect_list_dialog_button_cancel"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginBottom="35dp"
        android:layout_marginEnd="20dp"
        android:visibility="gone"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/connect_list_dialog_button_re">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_dialog_select"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:text="@string/back_to_home"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>