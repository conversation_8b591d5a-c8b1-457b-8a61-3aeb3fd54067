<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:id="@+id/dvr_root"
    tools:context=".features.dvr.DvrFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/toptoolbar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_back"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" >
            <ImageView
                android:id="@+id/ib_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/set_back" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <TextView
            android:id="@+id/tx_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/dvr"
            android:textColor="#323232"
            android:textSize="17sp"
            android:textStyle="bold"
            android:layout_marginTop="7dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/menu_view"
        android:src="@drawable/menu"
        android:layout_width="21dp"
        android:visibility="gone"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/toptoolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/toptoolbar"
        android:layout_marginEnd="14dp"
        />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/media_background"
        android:layout_width="match_parent"
        android:layout_height="211dp"
        android:background="@color/black"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toptoolbar">
        <com.link.riderdvr.widget.IVideoView
            android:id="@+id/ivideoview"
            android:layout_width="match_parent"
            android:layout_height="211dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <com.link.riderlink.features.dvr.DvrButtonView
        android:id="@+id/online_dvr"
        android:layout_width="0dp"
        android:layout_height="44dp"
        app:layout_constraintRight_toLeftOf="@id/locate_dvr"
        app:layout_constraintTop_toBottomOf="@+id/media_background"
        app:layout_constraintLeft_toLeftOf="parent"
        />

    <com.link.riderlink.features.dvr.DvrButtonView
        android:id="@+id/locate_dvr"
        android:layout_width="0dp"
        android:layout_height="44dp"
        app:layout_constraintLeft_toRightOf="@id/online_dvr"
        app:layout_constraintTop_toBottomOf="@+id/media_background"
        app:layout_constraintRight_toRightOf="parent"
        />


    <com.link.riderlink.features.dvr.LeftWipeRecyclerView
        android:id="@+id/dvr_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="22dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/online_dvr" />
</androidx.constraintlayout.widget.ConstraintLayout>