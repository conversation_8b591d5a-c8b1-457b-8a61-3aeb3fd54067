<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:id="@+id/detail_root">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_back"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="7dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/set_back" >
        <ImageView
            android:id="@+id/ib_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/set_back" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/logcat"
        android:textColor="#323232"
        android:textSize="17sp"
        android:layout_marginTop="4dp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tx_share"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/share"
        android:textColor="#323232"
        android:textSize="17sp"
        android:textStyle="bold"
        android:layout_marginEnd="12dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title"/>

    <TextView
        android:id="@+id/tx_detailed"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:textSize="19sp"
        android:text="@string/detailed_info"
        android:scrollbars="vertical"
        android:textColor="#323232"
        android:layout_marginTop="62dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:textIsSelectable="true"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>