<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:textColor="#000000"
            android:text="@string/phone_number" />
        <EditText
            android:id="@+id/mobileid"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:inputType="textPersonName"
            android:cursorVisible="false"
            android:text=""
            android:layout_weight="1"/>
        <Button
            android:id="@+id/btnCopyMobileid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="0dp"
            android:layout_marginRight="0dp"
            android:layout_marginTop="0dp"
            android:minHeight="28dp"
            android:text="@string/click_to_copy"
            android:textColor="#fff"
            android:background="#D41A5E"
            android:textSize="16sp" />
    </LinearLayout>
</LinearLayout>