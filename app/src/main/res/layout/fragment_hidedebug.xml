<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:id="@+id/hide_root">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_back"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="4dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" >
        <ImageView
            android:id="@+id/ib_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/set_back" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/debug"
        android:textColor="#323232"
        android:textSize="17sp"
        android:layout_marginTop="7dp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_app"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/ll_back">
        <ImageView
            android:id="@+id/background_app"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/item_background_s"
            android:layout_marginTop="10dp"/>
        <TextView
            android:id="@+id/tv_app"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="10dp"
            android:text="@string/version"
            android:textSize="14sp"
            android:textColor="#191919"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16.5dp"
            android:layout_marginTop="15dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_riderlink"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/item_app">
        <ImageView
            android:id="@+id/background_riderlink"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/item_background_s"
            android:layout_marginTop="10dp"/>
        <TextView
            android:id="@+id/tv_riderlink"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="10dp"
            android:text="@string/riderlink_version"
            android:textSize="14sp"
            android:textColor="#191919"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16.5dp"
            android:layout_marginTop="15dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_autolink"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/item_riderlink">
        <ImageView
            android:id="@+id/background_autolink"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/item_background_s"
            android:layout_marginTop="10dp"/>
        <TextView
            android:id="@+id/tv_autolink"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="10dp"
            android:text="@string/autolink_version"
            android:textSize="14sp"
            android:textColor="#191919"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16.5dp"
            android:layout_marginTop="15dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_productkey"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/item_autolink">
        <ImageView
            android:id="@+id/background_productkey"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/item_background_s"
            android:layout_marginTop="10dp"/>
        <TextView
            android:id="@+id/tv_productkey"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="10dp"
            android:text="@string/product_key"
            android:textSize="14sp"
            android:textColor="#191919"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16.5dp"
            android:layout_marginTop="15dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_uuid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/item_productkey">
        <ImageView
            android:id="@+id/background_uuid"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/item_background_s"
            android:layout_marginTop="10dp"/>
        <TextView
            android:id="@+id/tv_uuid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="10dp"
            android:text="@string/uuid_label"
            android:textSize="14sp"
            android:textColor="#191919"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16.5dp"
            android:layout_marginTop="15dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_crash"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/item_uuid">
        <ImageView
            android:id="@+id/background_crash"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/item_background_s"
            android:layout_marginTop="10dp"/>
        <TextView
            android:id="@+id/tv_crash"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="10dp"
            android:text="@string/crash_log"
            android:textSize="14sp"
            android:textColor="#191919"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16.5dp"
            android:layout_marginTop="15dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_logcat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/item_crash">
        <ImageView
            android:id="@+id/background_logcat"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/item_background_s"
            android:layout_marginTop="10dp"/>
        <TextView
            android:id="@+id/tv_logcat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="10dp"
            android:text="@string/logcat_log"
            android:textSize="14sp"
            android:textColor="#191919"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16.5dp"
            android:layout_marginTop="15dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_logcat_sw"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/item_logcat">
        <ImageView
            android:id="@+id/background_logcat_sw"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/item_background_s"
            android:layout_marginTop="10dp"/>
        <TextView
            android:id="@+id/tv_logcat_sw"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="10dp"
            android:text="@string/open_logcat"
            android:textSize="14sp"
            android:textColor="#191919"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <Switch
            android:id="@+id/btn_switch_loge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Switch"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16.5dp"
            android:layout_marginTop="15dp"
            tools:ignore="UseSwitchCompatOrMaterialXml" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_simulation_navi"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/item_logcat_sw">
        <ImageView
            android:id="@+id/background_simulation_sw"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/item_background_s"
            android:layout_marginTop="10dp"/>
        <TextView
            android:id="@+id/tv_simulation_sw"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginStart="10dp"
            android:text="@string/simulated_navigation"
            android:textSize="14sp"
            android:textColor="#191919"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <Switch
            android:id="@+id/btn_switch_navi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Switch"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16.5dp"
            android:layout_marginTop="15dp"
            tools:ignore="UseSwitchCompatOrMaterialXml" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>