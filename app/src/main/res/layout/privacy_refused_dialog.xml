<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="221dp"
    android:layout_marginRight="20dp"
    android:layout_marginLeft="20dp"
    android:background="#00000000">

    <View
        android:id="@+id/privacy_dialog_refused_background"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/privacy_dialog_refused_title"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:layout_marginTop="30dp"
        android:text="@string/policy_refused_title"
        android:textColor="@color/black_tx"
        android:textSize="18sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <TextView
        android:id="@+id/privacy_dialog_refused_content"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:textColor="@color/black_tx"
        android:layout_marginTop="73dp"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:textSize="14sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/privacy_dialog_button_refused_exit"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginBottom="30dp"
        android:layout_marginEnd="12dp"
        android:layout_marginStart="24dp"
        app:layout_constraintRight_toLeftOf="@+id/privacy_dialog_button_refused_ok"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_dialog_select"
            android:alpha="0.1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:text="@string/exit_app"
            android:textSize="14sp"
            android:textColor="#5C7BD7"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/privacy_dialog_button_refused_ok"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginBottom="30dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintLeft_toRightOf="@+id/privacy_dialog_button_refused_exit"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintRight_toRightOf="parent">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_dialog_select"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:text="@string/agree_and_continue"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>