<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dl_device_item"
    android:layout_width="335dp"
    android:layout_height="69dp">

    <TextView
        android:id="@+id/tv_device_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:text="@string/device_name"
        android:textColor="#323232"
        android:textSize="14sp"
        android:layout_marginTop="14dp"
        android:layout_marginStart="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_device_address"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:text="@string/address"
        android:textColor="#8C909E"
        android:textSize="12sp"
        android:layout_marginTop="3dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_device_name"
        app:layout_constraintLeft_toLeftOf="@+id/tv_device_name" />

    <TextView
        android:id="@+id/tv_device_distance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:text="@string/distance_label"
        android:textColor="#8C909E"
        android:textSize="12sp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="@+id/tv_device_address"
        app:layout_constraintLeft_toRightOf="@+id/tv_device_address"/>

    <TextView
        android:id="@+id/tv_device_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:text="@string/connection_status"
        android:textColor="#8C909E"
        android:textSize="12sp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="25dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <ImageView
        android:id="@+id/img_device_state"
        android:layout_width="8dp"
        android:layout_height="6dp"
        android:visibility="gone"
        android:src="@drawable/connect_success"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="31dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_device_state"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E7E9EF"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>