<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/set_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/toptoolbar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_back"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ib_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@null"
                android:src="@drawable/set_back"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tx_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:text="@string/settings_title"
            android:textColor="#323232"
            android:textSize="17sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/background1"
        android:layout_width="match_parent"
        android:layout_height="82dp"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="9dp"
        android:layout_marginRight="12dp"
        android:background="@drawable/item_background_d"
        android:contentDescription="@null"
        app:layout_constraintTop_toBottomOf="@+id/toptoolbar" />

    <ImageView
        android:contentDescription="@null"
        android:id="@+id/background2"
        android:layout_width="match_parent"
        android:layout_height="82dp"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:background="@drawable/item_background_d"
        app:layout_constraintTop_toBottomOf="@+id/background1" />

    <ImageView
        android:contentDescription="@null"
        android:id="@+id/background3"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:background="@drawable/item_background_s"
        app:layout_constraintTop_toBottomOf="@+id/background2" />

    <ImageView
        android:contentDescription="@null"
        android:id="@+id/background4"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="12dp"
        android:background="@drawable/item_background_s"
        app:layout_constraintTop_toBottomOf="@+id/background3" />

    <ImageView
        android:contentDescription="@null"
        android:id="@+id/set_night"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="14dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/item_moon"
        app:layout_constraintLeft_toLeftOf="@+id/background1"
        app:layout_constraintTop_toTopOf="@+id/background1" />

    <TextView
        android:id="@+id/tv_night"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="48dp"
        android:layout_marginTop="12dp"
        android:text="@string/night_mode"
        android:textColor="#191919"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="@+id/background1"
        app:layout_constraintTop_toTopOf="@+id/background1" />

    <TextView
        android:id="@+id/tv_follow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="48dp"
        android:layout_marginTop="48dp"
        android:text="@string/daynight_auto"
        android:textColor="#191919"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="@+id/background1"
        app:layout_constraintTop_toTopOf="@+id/background1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_help"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:layout_marginLeft="26dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="26dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/background2">

        <ImageView
            android:id="@+id/set_help"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/circle_help"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/tv_help"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="2dp"
            android:text="@string/help_center"
            android:textColor="#191919"
            android:textSize="14sp"
            app:layout_constraintLeft_toRightOf="@+id/set_help"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="2.5dp"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_private"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:layout_marginLeft="26dp"
        android:layout_marginTop="48dp"
        android:layout_marginRight="26dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/background2">

        <ImageView
            android:id="@+id/set_private"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/edit_alt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_private"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="2dp"
            android:text="@string/privacy_policy"
            android:textColor="#191919"
            android:textSize="14sp"
            app:layout_constraintLeft_toRightOf="@+id/set_private"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="2.5dp"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_about"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:layout_marginLeft="26dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="26dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/background3">

        <ImageView
            android:id="@+id/set_about"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/circle_information"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/tv_about"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="2dp"
            android:text="@string/about"
            android:textColor="#191919"
            android:textSize="14sp"
            app:layout_constraintLeft_toRightOf="@+id/set_about"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="2.5dp"
            android:src="@drawable/next_fragment"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_del"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:layout_marginLeft="26dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="26dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/background4">

        <ImageView
            android:id="@+id/set_del"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/bluetooth_audio"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/tv_del"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="2dp"
            android:text="@string/clear_config"
            android:textColor="#191919"
            android:textSize="14sp"
            app:layout_constraintLeft_toRightOf="@+id/set_del"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <Switch
        android:id="@+id/btn_switch_mode"
        style="@style/Switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="14dp"
        app:layout_constraintRight_toRightOf="@+id/background1"
        app:layout_constraintTop_toTopOf="@+id/background1"
        tools:ignore="UseSwitchCompatOrMaterialXml" />

    <Switch
        android:id="@+id/btn_switch_follow"
        style="@style/Switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="14dp"
        app:layout_constraintRight_toRightOf="@+id/background1"
        app:layout_constraintTop_toTopOf="@+id/background1"
        tools:ignore="UseSwitchCompatOrMaterialXml" />
</androidx.constraintlayout.widget.ConstraintLayout>