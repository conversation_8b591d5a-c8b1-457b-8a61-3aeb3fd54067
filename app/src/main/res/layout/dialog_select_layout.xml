<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="250dp"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/ds_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:padding="10dp"
        android:text="@string/notice"
        android:textColor="#212121"
        android:textSize="16sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/line" />


    <TextView
        android:id="@+id/ds_text"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:lineSpacingMultiplier="1.2"
        android:paddingLeft="10dp"
        android:paddingTop="20dp"
        android:paddingRight="10dp"
        android:paddingBottom="10dp"
        android:gravity="center"
        android:textColor="#56595e"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/line" />
    <LinearLayout
        android:id="@+id/select_view"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/select_cancel"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@drawable/btn_rule_selector"
            android:gravity="center"
            android:padding="10dp"
            android:text="@string/select_disagree"
            android:textColor="#212121"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/select_ok"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@drawable/btn_rule_right_selector"
            android:gravity="center"
            android:padding="10dp"
            android:text="@string/select_agree"
            android:textColor="#3b81f1"
            android:textSize="15sp" />

        <View
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/line" />
    </LinearLayout>

</LinearLayout>