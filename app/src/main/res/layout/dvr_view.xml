<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dvr_cl"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000">
    <TextView
        android:id="@+id/title_btn"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:text="@string/all_dvr"
        android:textSize="14sp"
        android:textColor="@color/blue_tx"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <ImageView
        android:id="@+id/dvr_selected_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/dvr_selected"
        android:layout_marginTop="2dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_btn"/>


</androidx.constraintlayout.widget.ConstraintLayout>
