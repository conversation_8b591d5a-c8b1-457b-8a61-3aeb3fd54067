<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_logcat_item"
    android:layout_width="match_parent"
    android:layout_height="56dp">

    <TextView
        android:id="@+id/tx_logcat_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lines="1"
        android:text="@string/file_name"
        android:textColor="#323232"
        android:textSize="16sp"
        android:layout_marginStart="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    <CheckBox
        android:id="@+id/checkbox_logcat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:onClick="onCheckboxClicked"
        android:layout_marginEnd="8dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>