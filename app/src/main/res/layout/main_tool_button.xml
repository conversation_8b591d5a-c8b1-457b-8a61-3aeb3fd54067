<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_tool_button_root"
        android:clickable="true"
        android:background="@drawable/main_tool_background_select"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/main_tool_bitmap"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>
        <TextView
            android:id="@+id/main_tool_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9.3dp"
            android:layout_marginStart="38.7dp"
            android:text="@string/title_label"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>
        <TextView
            android:id="@+id/main_tool_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9.3dp"
            android:text="@string/text_content"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:layout_constraintTop_toBottomOf="@+id/main_tool_title"
            app:layout_constraintLeft_toLeftOf="@+id/main_tool_title"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>