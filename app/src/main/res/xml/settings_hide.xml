<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <Preference
        app:iconSpaceReserved="false"
        android:key="app"
        android:title="App版本" />
    <Preference
        app:iconSpaceReserved="false"
        android:key="riderlink"
        android:title="Riderlink版本" />
    <Preference
        app:iconSpaceReserved="false"
        android:key="autolink"
        android:title="Autolink版本" />
    <Preference
        app:iconSpaceReserved="false"
        android:key="productkey"
        android:title="Product key" />
    <Preference
        app:iconSpaceReserved="false"
        android:key="uuid"
        android:title="UUID" />
    <Preference
        app:iconSpaceReserved="false"
        android:key="crash"
        android:title="Crash日志" />
    <Preference
        app:iconSpaceReserved="false"
        android:key="logcat"
        android:title="logcat日志" />
    <SwitchPreference
        app:iconSpaceReserved="false"
        android:key="logcat_sw"
        android:defaultValue="false"
        android:title="开启logcat日志" />
    <SwitchPreference
        app:iconSpaceReserved="false"
        android:key="simulation_sw"
        android:defaultValue="false"
        android:title="开启模拟导航" />
</androidx.preference.PreferenceScreen>