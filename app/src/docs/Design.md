# 睿连智行 App 设计文档

## 1. 引言

本文档旨在阐述睿连智行 (RiderLink) Android 应用程序的设计。它将涵盖应用程序的架构、主要功能模块、关键组件以及它们之间的交互。

## 2. 应用概述

睿连智行是一款旨在提升摩托车骑行体验的应用程序。它通过与摩托车仪表盘或其他支持RiderLink功能的设备互联，提供导航、DVR（行车记录）、音乐播放、通知管理等功能。

## 3. 整体架构

应用程序遵循基于 Model-View-ViewModel (MVVM) 模式的现代 Android 架构。

*   **视图 (View)**: 主要由 Activities 和 Fragments 构成，负责展示UI和接收用户输入。
*   **视图模型 (ViewModel)**: 负责处理UI相关的业务逻辑，管理和暴露UI状态给视图。
*   **模型 (Model)**: 包括数据源 (本地数据库、网络API)、仓库 (Repository) 以及业务逻辑实体。

### 3.1 主要组件

*   **`MainApp.kt`**: 应用程序的入口点，负责全局初始化，例如屏幕适配库 (AutoSize)、弹窗库 (DialogX)、Toast库 (ToastUtils) 和崩溃处理程序。
*   **`MainActivity.kt`**: 应用的主 Activity，承载各个功能模块的 Fragment，并处理全局的UI事件和生命周期管理。
*   **`MainViewModel.kt`**: `MainActivity` 的 ViewModel，管理主界面的UI状态，如启动页的显示/隐藏、悬浮按钮的可见性等。
*   **`RiderLink.kt` (核心库)**: 封装了与外部设备（如仪表盘）连接和通信的核心逻辑。
*   **`RiderService.kt` (核心库)**: 负责处理来自外部设备的消息和数据。
*   **`AppDatabase.kt`**: Room 数据库的定义，用于存储本地数据，例如搜索历史。

## 4. 功能模块

### 4.1 核心功能

#### 4.1.1 设备连接与通信

*   **连接管理**: 通过蓝牙 (BLE) 和 Wi-Fi Direct (P2P) 实现与外部设备的连接。
*   **数据同步**: 在App和连接的设备之间同步数据，例如导航指令、DVR文件列表、通知信息等。
*   **状态管理**: `ConnectInfoMange.kt` 和相关的ViewModel负责跟踪和更新连接状态，并在UI上展示给用户。

#### 4.1.2 UI和主题管理

*   **`ThemeManager.kt`**: 负责管理应用的主题（日间/夜间模式），并允许用户切换。它还提供了根据当前主题动态加载资源的机制。
*   **屏幕适配**: 使用 `AutoSize` 库进行屏幕适配，确保在不同尺寸和分辨率的设备上拥有一致的UI体验。设计尺寸在 `MainApp.kt` 中配置。
*   **自定义控件**: `widgets` 目录下包含了一些自定义UI控件，如 `DragFloatActionButton.kt` (可拖动悬浮按钮)、`LockScreenView.kt` (锁屏视图) 等。

### 4.2 主要特性模块

#### 4.2.1 关于 (`features/about`)

*   `AboutFragment.kt`: 显示应用的版权信息和版本号。包含一个隐藏的调试入口（通过多次点击图片触发）。

#### 4.2.2 用户协议 (`features/agreement`)

*   `AgreementFragment.kt`: 使用 WebView 加载并显示用户协议 (`agreement_cn.html`)。

#### 4.2.3 DVR (行车记录) (`features/dvr`)

*   **`DvrFragment.kt`**: 显示DVR文件列表（在线和本地），提供视频播放、下载、删除等功能。
*   **`DvrViewModel.kt`**: 管理DVR模块的UI状态和业务逻辑，包括获取文件列表、处理用户操作、管理下载任务等。
*   **`DvrAdapter.kt`**: RecyclerView 适配器，用于显示DVR文件项。
*   **`ControllerViewManager.kt`**: 管理视频播放器的控制界面。
*   **视频播放**: 集成了 `IVideoView` 和 `MyMediaController` (自定义媒体控制器) 进行视频播放。
*   **文件下载**: 通过 `LocalManager` (核心库) 实现DVR文件的下载功能，并使用 Room 数据库 (`DownLoadTaskRepository`) 管理下载任务。
*   **缩略图**: `ThumbnailUtils.java` (核心库) 用于生成视频缩略图。

#### 4.2.4 帮助中心 (`features/help`)

*   `HelpFragment.kt`: 使用 WebView 加载并显示帮助文档 (`help.html`, `help_cn.html` 及其夜间模式版本)。文档内容包含连接帮助、防止自动断开连接的技巧以及常见手机型号的设置示例。

#### 4.2.5 隐藏/调试功能 (`features/hide`)

*   `HideDebugFragment.kt`: 提供一个调试菜单入口，包含查看应用版本、RiderLink版本、Autolink版本、产品密钥、设备UUID以及日志管理等功能。
*   **日志管理**:
    *   `LogCatFragment.kt`: 显示和管理应用日志文件（logcat和crash日志）。支持查看、删除和分享日志。
    *   `DetailedFragment.kt`: 显示单个日志文件的详细内容。
    *   `LogcatHelper.java`: 工具类，用于捕获和保存应用的 logcat 日志。
    *   `CrashHandler.java`: 全局崩溃处理器，捕获未处理的异常并保存到日志文件。
*   **版本信息**:
    *   `AppVersionFragment.kt`: 显示应用自身的版本信息。
    *   `RiderLinkVersionFragment.kt`: 显示RiderLink库的版本信息。
    *   `AutoLinkVersionFragment.kt`: 显示Autolink库的版本信息。
*   **设备信息**:
    *   `ProductKeyFragment.kt`: 显示设备的产品密钥。
    *   `UuidFragment.kt`: 显示设备的UUID。
*   **开关**:
    *   日志抓取开关: 控制是否启用 `LogcatHelper` 进行日志记录。
    *   模拟导航开关: 控制 `RiderLink` 实例的导航类型。

#### 4.2.6 "我的" / 设置 (`features/my`)

*   `SettingFragment.kt`: 提供应用设置选项，如：
    *   夜间模式切换
    *   跟随系统主题切换
    *   帮助中心入口
    *   隐私政策入口
    *   关于入口
    *   清除已保存的连接配置

#### 4.2.7 隐私政策 (`features/privacy`)

*   `PrivacyFragment.kt`: 使用 WebView 加载并显示隐私政策文档 (`privacy.html`, `privacy_cn.html` 及其夜间模式版本)。

### 4.3 支持组件

#### 4.3.1 数据库 (`database`)

*   `AppDatabase.kt`: 定义了应用的 Room 数据库实例。
*   `UserHistoryDao.kt` (位于 `features/search/data/source/local/db/dao`): 用于访问搜索历史的数据访问对象 (DAO)。
*   `SearchAddress.kt` (位于 `features/search/data/source/local/db/model`): 搜索历史的数据模型。

#### 4.3.2 通知 (`notification`)

*   `NotifyService.kt`: 一个 `NotificationListenerService`，用于监听系统通知，并将相关信息（应用名、标题、内容）通过 `RiderService` 发送给连接的设备。

#### 4.3.3 服务 (`service`)

*   `RiderLinkService.kt`: 应用的主要后台服务，负责维持前台服务状态，确保应用在后台的持续运行，特别是在媒体投屏 (mediaProjection) 场景下。

#### 4.3.4 工具类 (`utils`)

*   `ThemeManager.kt`: 管理应用主题和动态资源加载。
*   `LogcatHelper.java`: 应用日志抓取工具。
*   `CrashHandler.java`: 应用崩溃捕获和记录。
*   `PermissionsUtil.kt`: 处理应用权限请求和相关的对话框显示。
*   `ContextExt.kt`: 提供Context的扩展函数，简化DataStore和偏好设置的读写。
*   `FlowExt.kt`: 提供 Kotlin Flow 的扩展函数，简化响应式编程。
*   其他工具类：如 `ConnectUtils.kt` (网络连接判断), `ShareFileUtils.kt` (文件分享), `ScreenBrightnessUtils.kt` (屏幕亮度工具) 等。

#### 4.3.5 控件 (`widgets`)

*   包含自定义的UI控件，如主页的连接按钮 (`MainConnectView.kt`)、工具按钮 (`MainToolView.kt`)、可拖动悬浮按钮 (`DragFloatActionButton.kt`)、锁屏视图 (`LockScreenView.kt`) 和自定义对话框 (`SelectDialog.kt`) 等。

## 5. 数据存储

*   **SharedPreferences**: 用于存储简单的配置信息，如夜间模式开关、日志抓取开关等。
*   **Room 数据库**:
    *   存储用户搜索历史 (`SearchAddress`)。
    *   存储DVR下载任务 (`DownLoadTask` - 位于 `riderdvr` 库中)。
*   **DataStore (Proto)**: `user_prefs.proto` 定义了用户偏好设置的数据结构，用于存储与设备功能支持相关的信息 (如是否支持DVR, 导航等)。`ContextExt.kt` 提供了读写 `UserPreferences` 的方法。
*   **文件存储**:
    *   应用日志和崩溃日志存储在应用私有目录。
    *   下载的DVR文件存储在应用私有目录 (`LocalManager` 管理)。
    *   WebView 使用的 HTML 文件 (帮助、隐私、协议) 存储在 `assets` 目录。

## 6. 权限管理

应用在 `AndroidManifest.xml` 中声明了多项权限，包括：

*   前台服务 (`FOREGROUND_SERVICE`, `FOREGROUND_SERVICE_MEDIA_PROJECTION`)
*   外部存储读写 (针对旧版本Android `WRITE_EXTERNAL_STORAGE`, `READ_EXTERNAL_STORAGE`, `MANAGE_EXTERNAL_STORAGE`)
*   悬浮窗 (`SYSTEM_ALERT_WINDOW`)
*   后台定位 (`ACCESS_BACKGROUND_LOCATION`)
*   网络 (`INTERNET`)
*   电话状态 (`READ_PHONE_STATE`)
*   忽略电池优化 (`REQUEST_IGNORE_BATTERY_OPTIMIZATIONS`)
*   安装包 (`REQUEST_INSTALL_PACKAGES`)
*   通知监听 (`ACTION_NOTIFICATION_LISTENER_SETTINGS`)
*   WiFi状态 (`ACCESS_WIFI_STATE`, `CHANGE_WIFI_STATE`)
*   视频截图 (`CAPTURE_VIDEO_OUTPUT` - 签名保护)
*   查询所有包 (`QUERY_ALL_PACKAGES`)
*   发送通知 (`POST_NOTIFICATIONS`)
*   蓝牙 (`BLUETOOTH_LE`, `BLUETOOTH_CONNECT`, `BLUETOOTH_SCAN`, `BLUETOOTH_ADVERTISE`)
*   附近WiFi设备 (`NEARBY_WIFI_DEVICES`)

`PermissionsUtil.kt` 用于辅助处理运行时权限请求的逻辑。

## 7. 未来展望

*   模块化重构：将核心库 (RiderLink, RiderService) 和功能模块进一步解耦。
*   UI/UX 优化：持续改进用户界面和用户体验。
*   性能优化：针对关键路径进行性能分析和优化。 