# 睿连智行 App - HERE 地图模块设计文档

## 1. 引言

本文档专注于描述睿连智行 (RiderLink) Android 应用程序中与 HERE 地图服务相关模块的设计。它将详细介绍地图显示、路线规划、搜索功能以及相关的用户界面和数据管理。

## 2. 模块概述

HERE 地图模块是睿连智行应用的核心组成部分之一，为用户提供基于 HERE SDK 的地图、导航和位置服务。主要功能包括：

*   **地图展示与交互**: 显示地图，允许用户进行缩放、平移等操作。
*   **定位服务**: 获取并显示用户当前位置。
*   **路线规划**: 根据起点和终点规划驾车/骑行路线。
*   **导航**: 提供turn-by-turn导航指引。
*   **搜索功能**: 允许用户搜索地点，并管理搜索历史。

## 3. 架构与主要组件

与应用主体架构类似，HERE 模块内的功能也倾向于遵循 MVVM 模式。

### 3.1 主要 Fragment

*   **`HomeFragment.kt`**:
    *   作为用户与设备连接的主要入口，展示设备列表。
    *   管理蓝牙扫描、设备连接/断开状态。
    *   提供启动导航、DVR 和音乐（QQ音乐）等功能的入口。
    *   处理隐私政策和服务协议的展示与同意逻辑。
    *   包含一个可拖动的悬浮按钮（FAB），用于启动锁屏功能。
    *   根据连接状态和设备能力动态调整UI元素。
*   **`MapFragment.kt`**:
    *   负责显示基础地图界面 (`SimpleMapview`)。
    *   处理地图长按选点、定位到当前位置、启动搜索等交互。
    *   与 `MapViewModel.kt` 配合，更新地图状态和显示。
*   **`RouteFragment.kt`**:
    *   负责展示路线规划和导航界面 (`MapRouteView`)。
    *   处理路线计算、导航开始/停止等逻辑。
    *   根据导航状态调整UI（例如，全屏显示导航视图）。
    *   与 `RouteViewModel.kt` 交互，获取路线数据和导航状态。
*   **`SearchDialogFragment.kt`**:
    *   提供一个地址搜索对话框界面。
    *   显示搜索建议和历史记录。
    *   用户选择地址后，将结果回调给调用方 (通常是 `MapFragment` 或 `RouteFragment`)。
    *   与 `SearchViewModel.kt` 交互，处理搜索逻辑和历史记录管理。

### 3.2 主要 ViewModel

*   **`DeviceViewModel.kt`**: (位于 `home` 包下)
    *   管理蓝牙设备扫描、连接的状态和设备列表。
    *   处理与 `RiderLink` 核心库的蓝牙相关回调。
*   **`HomeViewModel.kt`**: (位于 `home` 包下)
    *   管理主屏幕的UI状态，包括连接状态、镜像状态、功能按钮的可见性等。
    *   处理定位请求、导航模式切换、镜像功能的启动/停止。
    *   与 `RiderLink` 和 `RiderService` 交互，获取设备配置和连接状态。
*   **`MapViewModel.kt`**: (位于 `map` 包下)
    *   管理地图显示相关的状态，如地图主题。
    *   处理地理编码搜索（长按选点）、获取当前位置等。
    *   与 `RiderLink` 交互，获取地图和定位更新。
*   **`RouteViewModel.kt`**: (位于 `route` 包下)
    *   管理路线规划和导航相关的状态，如当前路线、导航状态 (开始、停止、到达目的地)等。
    *   处理路线计算请求，并在成功或失败时通知视图。
    *   与 `RiderLink` 交互，控制导航的开始和停止。
*   **`SearchViewModel.kt`**: (位于 `search` 包下)
    *   管理搜索历史记录和搜索结果。
    *   通过 `SearchRepository` 与本地数据库交互，存取搜索历史。
    *   调用 `RiderLink` 的搜索接口获取POI数据。

### 3.3 核心交互组件 (HERE SDK 相关)

*   **`RiderMap.kt` (核心库)**: 封装了与 HERE SDK 地图视图、定位、路线规划和搜索功能的直接交互。它是应用逻辑与 HERE SDK 功能之间的桥梁。
*   **`SimpleMapview` (自定义View)**: 可能是 `MapView` 的一个封装，提供了基础的地图显示和简单的交互接口。
*   **`MapRouteView` (自定义View)**: 可能是 `MapView` 的一个封装，专门用于显示路线和导航相关的UI元素及交互。

### 3.4 数据模型和仓库

*   **`SearchAddress.kt`**: Room实体类，定义了搜索历史记录的数据结构。
*   **`UserHistoryDao.kt`**: Room DAO接口，提供了对搜索历史数据库表的增删查操作。
*   **`LocalSearchDataSource.kt` / `LocalSearchDataSourceImpl.kt`**: 定义并实现了本地搜索数据源的接口，封装了与 `UserHistoryDao` 的交互。
*   **`SearchRepository.kt` / `SearchRepositoryImpl.kt`**: 定义并实现了搜索仓库的接口，是ViewModel访问搜索数据的入口。

## 4. 功能详解

### 4.1 设备连接与主页 (`home`)

*   `HomeFragment` 作为起始点，首先处理隐私政策和用户协议的展示。
*   用户同意后，开始请求必要的权限 (位置、蓝牙、附近设备)。
*   权限获取成功后，`DeviceViewModel` 和 `HomeViewModel` 协同 `RiderLink` 进行蓝牙设备扫描。
*   `DeviceAdapter` 在 `ConnectListDialog` (通过 `HomeDialogs.kt` 创建) 中显示扫描到的设备。
*   用户选择设备后，`DeviceViewModel` 通过 `RiderLink` 发起连接。
*   `HomeViewModel` 监听连接状态，并更新主界面的UI (连接状态文本、按钮可见性等)。
*   连接成功后，主页会显示设备支持的功能入口 (导航、DVR等)，并可能显示当前位置信息。
*   支持镜像投屏功能，通过 `MediaProjectionManager` 实现。

### 4.2 地图显示与交互 (`map`)

*   `MapFragment` 初始化 `SimpleMapview` 来展示地图。
*   `MapViewModel` 负责从 `RiderLink` 获取地图主题（日间/夜间）并通知视图更新。
*   用户可以通过地图控件进行平移、缩放。
*   **定位**:
    *   用户点击定位按钮，`MapFragment` -> `MapViewModel` -> `RiderLink` -> `RiderMap` 获取当前位置。
    *   获取到的位置信息通过 `MapViewEvent.UpdateCurrentPosition` 更新到 `MapFragment`。
*   **长按选点/反地理编码**:
    *   用户长按地图，`SimpleMapview` 将地理坐标传递给 `MapFragment`。
    *   `MapFragment` -> `MapViewModel` -> `RiderLink` -> `RiderMap` 执行反地理编码。
    *   解析出的地点信息 (`Place`) 会被构造成 `Destination` 对象，并通过 `MapViewEvent.UpdateSearchLocation` 更新到 `MapFragment`，在地图上添加标记并显示地点名称。
    *   选中的地点信息会通过 `ContextExt.saveDestination` 保存，供后续路线规划使用。
*   **搜索入口**:
    *   用户点击搜索按钮，会弹出 `SearchDialogFragment`。

### 4.3 地址搜索 (`search`)

*   `SearchDialogFragment` 提供搜索输入框。
*   用户输入关键词后，`SearchViewModel` 通过 `RiderLink` -> `RiderMap` 调用 HERE SDK 的搜索接口。
*   搜索结果 (`SearchResult`) 返回后，转换为 `List<SearchAddress>` 并更新到 `SearchViewState.result`，通过 `searchAdapter` 显示在结果列表中。
*   **历史记录**:
    *   `SearchViewModel` 在初始化时通过 `SearchRepository` 加载历史搜索记录，并通过 `SearchViewState.histories` 更新UI (`historyAdapter`)。
    *   当用户选择一个搜索结果或历史记录时，该记录会通过 `SearchViewModel.InsertHistory` 插入到数据库。
    *   用户可以长按历史记录项来删除它，通过 `SearchViewModel.DeleteHistory` 实现。
*   **结果选择**:
    *   用户从搜索结果或历史记录中选择一个地址后，`SearchDialogFragment` 会通过 `onAddressSelected` 回调将选中的 `SearchAddress` 对象传递给调用方 (例如 `MapFragment` 或 `RouteFragment`)。

### 4.4 路线规划与导航 (`route`)

*   `RouteFragment` 启动时，会从本地存储 (通过 `ContextExt.getDestination`) 读取之前选定的目的地信息。
*   同时，它会请求当前位置作为起点。
*   获取起点和终点坐标后，`RouteViewModel` 通过 `RiderLink` -> `RiderMap` 计算骑行/驾车路线。
*   计算出的多条路线 (`List<Route>`) 会更新到 `RouteViewState.routes`，并通过 `MapRouteView` 展示给用户。
*   `MapRouteView` 允许用户选择一条路线，并显示路线详情 (距离、预计时间等)。
*   **开始导航**:
    *   用户点击开始导航按钮。
    *   `RouteFragment` 首先检查并请求后台定位权限。
    *   权限获取后，`RouteViewModel` -> `RiderLink` -> `RiderMap` 启动导航。
    *   `RiderMap` 会通过 `MapCallback.onStartNavi` 和 `MapCallback.onNaviDataChanged` 等回调导航状态和数据。
    *   `RouteViewModel` 更新 `RouteViewState.navigationState` 为 `NaviState.STARTED`。
    *   UI会切换到导航视图，通常是全屏模式，隐藏状态栏和导航栏 (通过 `ThemeManager` 和 `changeSystemBarColorActually` 控制)。
*   **停止导航**:
    *   用户点击停止导航按钮 (或导航自动结束)。
    *   `RouteViewModel` -> `RiderLink` -> `RiderMap` 停止导航。
    *   导航状态更新为 `NaviState.STOPPED` 或 `NaviState.DESTINATION`。
    *   UI恢复到非导航状态。
*   **导航模式同步**: 导航状态 (如开始、停止、到达目的地、导航信息)会通过 `RiderService` 同步到连接的外部设备。
*   **断线重连与导航恢复**:
    *   `RouteViewModel` 中的 `ConnectCallback` 监听设备连接状态。
    *   如果导航过程中发生断连，`shouldResumeAfterReconnect` 标记会设为 `true`。
    *   当设备重新连接成功 (通过 `onClusterReady` 或 `onVideoChannelReady` 回调)，如果 `shouldResumeAfterReconnect` 为 `true`，则会自动尝试恢复之前的导航状态。

## 5. UI 与主题

*   **日间/夜间模式**:
    *   HERE 模块的各 Fragment 和自定义 View 都通过 `ThemeManager` 实现了对日间/夜间模式的适配。
    *   `MapFragment` 和 `RouteFragment` 中的地图视图也会根据主题切换相应的地图方案 (如 `MapScheme.NORMAL_DAY`, `MapScheme.NORMAL_NIGHT`)。
    *   夜间模式下，隐私政策和帮助文档会加载对应的 `_night.html` 版本。
*   **系统栏适配**:
    *   `MainActivity` 和各主要 Fragment (如 `RouteFragment`) 会根据当前主题和导航状态动态调整状态栏和导航栏的颜色及图标样式，以提供沉浸式体验。

## 6. 数据持久化

*   **搜索历史**: 使用 Room 数据库存储，通过 `SearchRepository` 进行管理。
*   **目的地信息**: `MapFragment` 中选定的目的地信息通过 `Context.saveDestination` (DataStore Preferences) 持久化，供 `RouteFragment` 读取并用于路线规划。

## 7. 权限

*   `app/src/here/AndroidManifest.xml` 中声明了 `ACCESS_BACKGROUND_LOCATION` 和 `INTERNET` 权限。
*   `HomeFragment` 和 `RouteFragment` 在需要时会通过 `XXPermissions` 请求位置权限和后台定位权限。
*   `PermissionsUtil.kt` 提供了显示权限请求说明对话框的辅助功能。

## 8. Assets 文件

*   `agreement_cn.html`: 用户协议 (中文)。
*   `privacy.html`, `privacy_cn.html`, `privacy_night.html`, `privacy_cn_night.html`: 隐私政策的不同语言和主题版本。
*   HERE 地图模块似乎没有直接使用 `help.html` 系列文件，这些文件由主应用的 `HelpFragment` 使用。

该设计文档为HERE地图模块提供了一个初步的概述。 