plugins {
    alias(libs.plugins.android.application).apply(false)
    alias(libs.plugins.android.library).apply(false)
    alias(libs.plugins.kotlin.android).apply(false)
    alias(libs.plugins.jetbrains.dokka).apply(false)
    alias(libs.plugins.google.protobuf).apply(false)
    id("com.google.devtools.ksp") version "1.9.25-1.0.20" apply false
}

tasks.register("clean", Delete::class) {
    delete(rootProject.layout.buildDirectory)
}
